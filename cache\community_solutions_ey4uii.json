{"data": {"problem": "sprite rendering performance issue", "solutions": [{"title": "GitHub Issue 解决方案: Performance issue vs Cocos2d-x v3.17", "author": "Yehsam23", "description": "Download Test Project:\r\n[TestClip.zip](https://github.com/axmolengine/axmol/files/10976641/TestClip.zip)\r\n\r\nThe following tests were conducted using an Android HTC U11 Plus with the build release APK.\r\n\r\nWhen using 200 ClippingNode and Sprite, there is a significant difference in performance between Axmol and Cocos2d-x v3.17. Axmol's FPS is approximately 9-10, while Cocos2d-x v3.17's FPS is approximately 37-40.\r\n\r\nAxmol:\r\n![ax_clip](https://user-images.githubusercontent.com/1954445/225224980-638", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/1121", "tags": []}, {"title": "GitHub Issue 解决方案: Occlusion Culling Support", "author": "anishkumar0712", "description": "We have noticed that Axmol makes draw calls for nodes fully occluded by others, impacting performance. \n\nFor example, when a Layer A(covering full screen) is rendered on top of another Layer B, draw calls are still made for Layer B, even though it's not visible to the user. We specifically need to call setVisible(false) to stop draw calls for those layers.\n\nDoes the engine support occlusion culling to skip rendering these layers? ", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/2362", "tags": []}, {"title": "GitHub Issue 解决方案: iOS ClippingNode performance issue?", "author": "rh101", "description": "@Yehsam23 Something isn't quite right regarding your results.  The first two screenshots look like they're taken at the same design resolution, but the last one, which you've set to 200 clipping zones, isn't from the test code that you have attached.  That last screenshot looks like it came from the cpp-tests, and it's at a completely different design resolution.  \r\n\r\nPlease do the 200 clipping zone test with a new project on Cocos2d-x v4, on the same device, similar to the one you've attached t", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/1094", "tags": []}, {"title": "GitHub Issue 解决方案: Instancing proposal", "author": "solan-solan", "description": "> One problem is how can we integrate a draw function in a InstancedMaterial class? it would become something renderable which I don't think is the good approach here\r\n\r\nAgree that it would cause confusion to engine architecture.\r\n\r\n> Having a draw function being called inside every instanced mesh defeats the purpose of having instancing, a custom data buffer that stores transformation matrices for each object needs to be updated when and only WHEN an object changes transformation, gets created,", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/1043", "tags": ["feature proposal"]}, {"title": "GitHub Issue 解决方案: Some RenderTexture issue", "author": "rh101", "description": "So, a quick test using a lookup to check if the command already exists in the vector actually fixed the problem, but I still can't quite pinpoint why there is a problem to begin with.  It fixed both the drawing and the crash, and there were way less entries in the vector (like 9 commands vs 100's).\r\n\r\n```\r\nvoid Renderer::processRenderCommand(RenderCommand* command)\r\n{\r\n...\r\n\r\n    case RenderCommand::Type::CUSTOM_COMMAND:\r\n        flush();\r\n        drawCustomCommand(command);\r\n        break;\r\n   ", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/966", "tags": ["bug"]}], "discussions": [{"title": "社区讨论：相关问题解决方案", "url": "https://github.com/axmolengine/axmol/discussions", "platform": "github", "replies": 0, "lastActivity": "2025-06-21T14:14:45.121Z", "tags": ["community", "discussion"]}], "relatedIssues": [{"number": 1121, "title": "Performance issue vs Cocos2d-x v3.17", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/1121", "createdAt": "2023-03-15T06:39:18Z", "updatedAt": "2023-03-22T01:19:29Z"}, {"number": 1094, "title": "iOS ClippingNode performance issue?", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/1094", "createdAt": "2023-03-03T07:56:16Z", "updatedAt": "2023-03-15T03:20:12Z"}, {"number": 966, "title": "Some RenderTexture issue", "state": "closed", "labels": ["bug"], "url": "https://github.com/axmolengine/axmol/issues/966", "createdAt": "2022-11-21T08:40:11Z", "updatedAt": "2022-11-29T09:09:04Z"}, {"number": 2362, "title": "Occlusion Culling Support", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/2362", "createdAt": "2025-02-03T06:20:30Z", "updatedAt": "2025-02-11T06:28:56Z"}, {"number": 1043, "title": "Instancing proposal", "state": "closed", "labels": ["feature proposal"], "url": "https://github.com/axmolengine/axmol/issues/1043", "createdAt": "2023-02-04T08:32:54Z", "updatedAt": "2023-07-29T06:29:12Z"}, {"number": 678, "title": "RenderTarget | render to texture", "state": "closed", "labels": ["HelpDesk"], "url": "https://github.com/axmolengine/axmol/issues/678", "createdAt": "2022-06-29T16:42:42Z", "updatedAt": "2022-08-13T18:09:59Z"}]}, "timestamp": 1750515285619, "ttl": 3600000, "key": "community_solutions_ey4uii"}