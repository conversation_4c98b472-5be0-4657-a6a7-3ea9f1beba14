/**
 * API参考服务
 * 负责获取和提供 Axmol API 参考文档
 */
import { SearchOptions, ToolResponse } from '../types/index.js';
export declare class ApiReferenceService {
    private readonly OFFICIAL_DOCS_BASE;
    private readonly GITHUB_RAW_BASE;
    private readonly AXMOL_REPO;
    private readonly CACHE_TTL;
    /**
     * 获取API参考文档
     */
    getApiReference(className: string, methodName?: string, options?: SearchOptions): Promise<ToolResponse>;
    /**
     * 从官方文档获取API参考
     */
    private getOfficialApiReference;
    /**
     * 生成可能的API页面URL
     */
    private generateApiUrls;
    /**
     * 解析官方API页面
     */
    private parseOfficialApiPage;
    /**
     * 提取方法信息
     */
    private extractMethods;
    /**
     * 解析方法签名
     */
    private parseMethodSignature;
    /**
     * 解析参数
     */
    private parseParameters;
    /**
     * 提取相关API
     */
    private extractRelatedApis;
    /**
     * 提取示例代码
     */
    private extractExamples;
    /**
     * 检测命名空间
     */
    private detectNamespace;
    /**
     * 检测源文件
     */
    private detectSourceFile;
    /**
     * 从源码获取API参考
     */
    private getSourceCodeApiReference;
    /**
     * 生成可能的头文件路径
     */
    private generateHeaderPaths;
    /**
     * 解析源码API
     */
    private parseSourceCodeApi;
    /**
     * 从源码提取方法
     */
    private extractMethodsFromSource;
    /**
     * 生成基础API参考
     */
    private generateBasicApiReference;
}
export declare const apiReferenceService: ApiReferenceService;
//# sourceMappingURL=apiReferenceService.d.ts.map