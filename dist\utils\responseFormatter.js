/**
 * 响应格式化工具
 * 负责将各种服务的响应格式化为用户友好的文本
 */
/**
 * 格式化文档搜索响应
 */
export function formatDocumentationResponse(data, metadata) {
    if (!data || data.length === 0) {
        return '❌ 未找到相关文档。请尝试使用不同的关键词搜索。';
    }
    let response = `📚 **Axmol 文档搜索结果** (${data.length} 个结果)\n\n`;
    data.forEach((resource, index) => {
        response += `### ${index + 1}. ${resource.title}\n`;
        response += `**类型**: ${getResourceTypeLabel(resource.type)}\n`;
        response += `**来源**: ${resource.source}\n`;
        response += `**相关性**: ${resource.relevanceScore}/10\n`;
        response += `**链接**: ${resource.url}\n`;
        if (resource.content) {
            response += `**摘要**: ${resource.content.substring(0, 200)}...\n`;
        }
        if (resource.matchedTerms.length > 0) {
            response += `**匹配关键词**: ${resource.matchedTerms.join(', ')}\n`;
        }
        response += '\n';
    });
    return response;
}
/**
 * 格式化代码示例响应
 */
export function formatCodeExamplesResponse(data, metadata) {
    if (!data || data.length === 0) {
        return '❌ 未找到相关代码示例。请尝试使用不同的功能关键词搜索。';
    }
    let response = `💻 **Axmol 代码示例** (${data.length} 个示例)\n\n`;
    data.forEach((example, index) => {
        response += `### ${index + 1}. ${example.title}\n`;
        response += `**语言**: ${example.language.toUpperCase()}\n`;
        response += `**平台**: ${example.platform || '通用'}\n`;
        response += `**描述**: ${example.description}\n`;
        if (example.sourceUrl) {
            response += `**源码链接**: ${example.sourceUrl}\n`;
        }
        response += `\n**代码示例**:\n`;
        response += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n\n`;
    });
    return response;
}
/**
 * 格式化API参考响应
 */
export function formatApiReferenceResponse(data, metadata) {
    if (!data) {
        return '❌ 未找到API参考信息。请检查类名是否正确。';
    }
    let response = `📖 **Axmol API 参考**\n\n`;
    response += `### ${data.className}\n`;
    response += `**命名空间**: ${data.namespace}\n`;
    response += `**描述**: ${data.description}\n`;
    response += `**源文件**: ${data.sourceFile}\n`;
    response += `**文档链接**: ${data.documentationUrl}\n\n`;
    if (data.methodName) {
        response += `#### 方法: ${data.methodName}\n`;
        if (data.returnType) {
            response += `**返回类型**: ${data.returnType}\n`;
        }
        if (data.parameters && data.parameters.length > 0) {
            response += `**参数**:\n`;
            data.parameters.forEach(param => {
                response += `- \`${param.type} ${param.name}\`${param.optional ? ' (可选)' : ''}: ${param.description}\n`;
                if (param.defaultValue) {
                    response += `  默认值: \`${param.defaultValue}\`\n`;
                }
            });
            response += '\n';
        }
    }
    if (data.examples && data.examples.length > 0) {
        response += `#### 使用示例\n`;
        data.examples.forEach((example, index) => {
            response += `**示例 ${index + 1}**: ${example.title}\n`;
            response += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n`;
            response += `${example.description}\n\n`;
        });
    }
    if (data.relatedApis && data.relatedApis.length > 0) {
        response += `#### 相关API\n`;
        data.relatedApis.forEach(api => {
            response += `- ${api}\n`;
        });
        response += '\n';
    }
    return response;
}
/**
 * 格式化构建问题响应
 */
export function formatBuildIssueResponse(data, metadata) {
    if (!data) {
        return '❌ 无法分析构建问题。请提供更详细的错误信息。';
    }
    let response = `🔧 **Axmol 构建问题诊断**\n\n`;
    response += `**平台**: ${data.platform}\n`;
    response += `**错误类型**: ${getBuildErrorTypeLabel(data.errorType)}\n`;
    response += `**错误信息**: ${data.errorMessage}\n\n`;
    if (data.solutions && data.solutions.length > 0) {
        response += `### 解决方案 (${data.solutions.length} 个)\n\n`;
        data.solutions.forEach((solution, index) => {
            response += `#### ${index + 1}. ${solution.title}\n`;
            response += `**优先级**: ${getPriorityLabel(solution.priority)}\n`;
            response += `**验证状态**: ${solution.verified ? '✅ 已验证' : '⚠️ 未验证'}\n`;
            response += `**描述**: ${solution.description}\n\n`;
            if (solution.steps && solution.steps.length > 0) {
                response += `**解决步骤**:\n`;
                solution.steps.forEach((step, stepIndex) => {
                    response += `${stepIndex + 1}. ${step}\n`;
                });
                response += '\n';
            }
            if (solution.codeChanges && solution.codeChanges.length > 0) {
                response += `**代码修改**:\n`;
                solution.codeChanges.forEach(change => {
                    response += `- **文件**: ${change.file}\n`;
                    response += `  **说明**: ${change.explanation}\n`;
                    if (change.oldCode) {
                        response += `  **原代码**: \`${change.oldCode}\`\n`;
                    }
                    response += `  **新代码**: \`${change.newCode}\`\n`;
                });
                response += '\n';
            }
            if (solution.configChanges && solution.configChanges.length > 0) {
                response += `**配置修改**:\n`;
                solution.configChanges.forEach(change => {
                    response += `- **文件**: ${change.file}\n`;
                    response += `  **设置**: ${change.setting} = ${change.value}\n`;
                    response += `  **说明**: ${change.explanation}\n`;
                });
                response += '\n';
            }
        });
    }
    if (data.relatedIssues && data.relatedIssues.length > 0) {
        response += `### 相关问题\n`;
        data.relatedIssues.forEach(issue => {
            response += `- ${issue}\n`;
        });
        response += '\n';
    }
    return response;
}
/**
 * 格式化迁移指南响应
 */
export function formatMigrationGuideResponse(data, metadata) {
    if (!data) {
        return '❌ 未找到迁移指南。请检查源引擎版本是否正确。';
    }
    let response = `🔄 **${data.fromEngine} 到 ${data.toEngine} 迁移指南**\n\n`;
    response += `**主题**: ${data.topic}\n\n`;
    if (data.changes && data.changes.length > 0) {
        response += `### API 变更 (${data.changes.length} 个)\n\n`;
        data.changes.forEach((change, index) => {
            response += `#### ${index + 1}. ${change.category}\n`;
            response += `**旧API**: \`${change.oldApi}\`\n`;
            response += `**新API**: \`${change.newApi}\`\n`;
            response += `**描述**: ${change.description}\n`;
            response += `**破坏性变更**: ${change.breaking ? '⚠️ 是' : '✅ 否'}\n`;
            if (change.migrationSteps && change.migrationSteps.length > 0) {
                response += `**迁移步骤**:\n`;
                change.migrationSteps.forEach((step, stepIndex) => {
                    response += `${stepIndex + 1}. ${step}\n`;
                });
            }
            response += '\n';
        });
    }
    if (data.examples && data.examples.length > 0) {
        response += `### 迁移示例\n\n`;
        data.examples.forEach((example, index) => {
            response += `#### ${example.title}\n`;
            response += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n`;
            response += `${example.description}\n\n`;
        });
    }
    if (data.commonIssues && data.commonIssues.length > 0) {
        response += `### 常见问题\n\n`;
        data.commonIssues.forEach((issue, index) => {
            response += `#### ${index + 1}. ${issue.errorMessage}\n`;
            response += `**平台**: ${issue.platform}\n`;
            response += `**类型**: ${getBuildErrorTypeLabel(issue.errorType)}\n`;
            if (issue.solutions && issue.solutions.length > 0) {
                response += `**解决方案**: ${issue.solutions[0].title}\n`;
            }
            response += '\n';
        });
    }
    return response;
}
/**
 * 格式化平台信息响应
 */
export function formatPlatformInfoResponse(data, metadata) {
    if (!data) {
        return '❌ 未找到平台特定信息。请检查平台名称是否正确。';
    }
    let response = `🎯 **${data.platform} 平台信息**\n\n`;
    response += `**主题**: ${data.topic}\n\n`;
    if (data.requirements && data.requirements.length > 0) {
        response += `### 系统要求\n`;
        data.requirements.forEach(req => {
            response += `- ${req}\n`;
        });
        response += '\n';
    }
    if (data.configuration && data.configuration.length > 0) {
        response += `### 配置设置\n`;
        data.configuration.forEach(config => {
            response += `#### ${config.file}\n`;
            response += `- **设置**: ${config.setting}\n`;
            response += `- **值**: ${config.value}\n`;
            response += `- **说明**: ${config.explanation}\n\n`;
        });
    }
    if (data.buildSteps && data.buildSteps.length > 0) {
        response += `### 构建步骤\n`;
        data.buildSteps.forEach((step, index) => {
            response += `${index + 1}. ${step}\n`;
        });
        response += '\n';
    }
    if (data.examples && data.examples.length > 0) {
        response += `### 配置示例\n`;
        data.examples.forEach((example, index) => {
            response += `#### ${example.title}\n`;
            response += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n`;
            response += `${example.description}\n\n`;
        });
    }
    if (data.commonIssues && data.commonIssues.length > 0) {
        response += `### 常见问题\n`;
        data.commonIssues.forEach((issue, index) => {
            response += `#### ${index + 1}. ${issue.errorMessage}\n`;
            if (issue.solutions && issue.solutions.length > 0) {
                response += `**解决方案**: ${issue.solutions[0].title}\n`;
            }
            response += '\n';
        });
    }
    return response;
}
/**
 * 获取资源类型标签
 */
function getResourceTypeLabel(type) {
    const labels = {
        'official_docs': '官方文档',
        'wiki': 'Wiki',
        'source': '源码',
        'example': '示例',
        'header': '头文件',
        'document': '文档',
        'web': '网络资源',
        'community': '社区'
    };
    return labels[type] || type;
}
/**
 * 获取构建错误类型标签
 */
function getBuildErrorTypeLabel(type) {
    const labels = {
        'compile': '编译错误',
        'link': '链接错误',
        'runtime': '运行时错误',
        'configuration': '配置错误'
    };
    return labels[type] || type;
}
/**
 * 获取优先级标签
 */
function getPriorityLabel(priority) {
    const labels = {
        'high': '🔴 高',
        'medium': '🟡 中',
        'low': '🟢 低'
    };
    return labels[priority] || priority;
}
/**
 * 格式化代码分析响应
 */
export function formatCodeAnalysisResponse(data, metadata) {
    if (!data) {
        return '❌ 代码分析失败。请检查代码格式是否正确。';
    }
    let response = `🔍 **Axmol 代码分析结果**\n\n`;
    response += `**语言**: ${data.language.toUpperCase()}\n`;
    response += `**代码长度**: ${data.code.length} 字符\n\n`;
    // 问题和错误
    if (data.issues && data.issues.length > 0) {
        response += `### 发现的问题 (${data.issues.length} 个)\n\n`;
        data.issues.forEach((issue, index) => {
            const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
            response += `#### ${index + 1}. ${icon} ${issue.message}\n`;
            response += `**行号**: ${issue.line}\n`;
            response += `**严重性**: ${getSeverityLabel(issue.severity)}\n`;
            response += `**规则**: ${issue.rule}\n\n`;
        });
    }
    // 改进建议
    if (data.suggestions && data.suggestions.length > 0) {
        response += `### 改进建议 (${data.suggestions.length} 个)\n\n`;
        data.suggestions.forEach((suggestion, index) => {
            response += `#### ${index + 1}. ${suggestion.message}\n`;
            response += `**行号**: ${suggestion.line}\n`;
            response += `**类别**: ${suggestion.category}\n`;
            response += `**原因**: ${suggestion.reason}\n`;
            response += `**建议代码**: \`${suggestion.suggestedCode}\`\n\n`;
        });
    }
    // 最佳实践
    if (data.bestPractices && data.bestPractices.length > 0) {
        response += `### 相关最佳实践 (${data.bestPractices.length} 个)\n\n`;
        data.bestPractices.forEach((practice, index) => {
            response += `#### ${index + 1}. ${practice.title}\n`;
            response += `**类别**: ${practice.category}\n`;
            response += `**描述**: ${practice.description}\n\n`;
        });
    }
    // 性能建议
    if (data.performance && data.performance.length > 0) {
        response += `### 性能优化建议 (${data.performance.length} 个)\n\n`;
        data.performance.forEach((perf, index) => {
            response += `#### ${index + 1}. ${perf.aspect}\n`;
            response += `**建议**: ${perf.recommendation}\n`;
            response += `**影响**: ${getImpactLabel(perf.impact)}\n`;
            if (perf.measurement) {
                response += `**预期效果**: ${perf.measurement}\n`;
            }
            response += '\n';
        });
    }
    // 安全检查
    if (data.security && data.security.length > 0) {
        response += `### 安全检查 (${data.security.length} 个)\n\n`;
        data.security.forEach((security, index) => {
            const icon = security.severity === 'critical' ? '🚨' : security.severity === 'high' ? '🔴' : '⚠️';
            response += `#### ${index + 1}. ${icon} ${security.description}\n`;
            response += `**类型**: ${security.type === 'vulnerability' ? '安全漏洞' : '最佳实践'}\n`;
            response += `**严重性**: ${getSeverityLabel(security.severity)}\n`;
            response += `**建议**: ${security.recommendation}\n\n`;
        });
    }
    return response;
}
/**
 * 格式化版本对比响应
 */
export function formatVersionComparisonResponse(data, metadata) {
    if (!data) {
        return '❌ 版本对比失败。请检查版本号是否正确。';
    }
    let response = `🔄 **Axmol 版本对比**\n\n`;
    response += `**功能**: ${data.feature}\n`;
    response += `**对比版本**: ${data.versions.join(' vs ')}\n\n`;
    if (data.changes && data.changes.length > 0) {
        response += `### 版本变更 (${data.changes.length} 个)\n\n`;
        data.changes.forEach((change, index) => {
            const icon = change.changeType === 'added' ? '✅' :
                change.changeType === 'removed' ? '❌' :
                    change.changeType === 'deprecated' ? '⚠️' : '🔄';
            response += `#### ${index + 1}. ${icon} ${change.description}\n`;
            response += `**版本**: ${change.version}\n`;
            response += `**变更类型**: ${getChangeTypeLabel(change.changeType)}\n`;
            response += `**影响**: ${getImpactLabel(change.impact)}\n\n`;
        });
    }
    if (data.compatibility && data.compatibility.length > 0) {
        response += `### 兼容性信息\n\n`;
        data.compatibility.forEach(compat => {
            const icon = compat.compatible ? '✅' : '❌';
            response += `- **${compat.version}**: ${icon} ${compat.notes}\n`;
        });
        response += '\n';
    }
    if (data.migrationNotes && data.migrationNotes.length > 0) {
        response += `### 迁移说明\n\n`;
        data.migrationNotes.forEach(note => {
            response += `- ${note}\n`;
        });
        response += '\n';
    }
    return response;
}
/**
 * 格式化最佳实践响应
 */
export function formatBestPracticesResponse(data, metadata) {
    if (!data || data.length === 0) {
        return '❌ 未找到相关最佳实践。请尝试使用不同的用例关键词。';
    }
    let response = `💡 **Axmol 最佳实践** (${data.length} 个建议)\n\n`;
    data.forEach((practice, index) => {
        response += `### ${index + 1}. ${practice.title}\n`;
        response += `**类别**: ${practice.category}\n`;
        response += `**用例**: ${practice.useCase}\n`;
        response += `**描述**: ${practice.description}\n\n`;
        if (practice.recommendations && practice.recommendations.length > 0) {
            response += `#### 建议\n`;
            practice.recommendations.forEach(rec => {
                response += `- ${rec}\n`;
            });
            response += '\n';
        }
        if (practice.examples && practice.examples.length > 0) {
            response += `#### 代码示例\n`;
            practice.examples.forEach(example => {
                response += `**${example.title}**:\n`;
                response += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n`;
                response += `${example.description}\n\n`;
            });
        }
        if (practice.antiPatterns && practice.antiPatterns.length > 0) {
            response += `#### 应避免的做法\n`;
            practice.antiPatterns.forEach(pattern => {
                response += `- ❌ ${pattern}\n`;
            });
            response += '\n';
        }
        if (practice.performance && practice.performance.length > 0) {
            response += `#### 性能影响\n`;
            practice.performance.forEach(perf => {
                response += `- **${perf.aspect}**: ${perf.recommendation}\n`;
                response += `  影响: ${getImpactLabel(perf.impact)}\n`;
                if (perf.measurement) {
                    response += `  预期效果: ${perf.measurement}\n`;
                }
            });
            response += '\n';
        }
    });
    return response;
}
/**
 * 格式化社区解决方案响应
 */
export function formatCommunityResponse(data, metadata) {
    if (!data) {
        return '❌ 未找到社区解决方案。请尝试使用不同的问题描述。';
    }
    let response = `🌐 **社区解决方案**\n\n`;
    response += `**问题**: ${data.problem}\n\n`;
    if (data.solutions && data.solutions.length > 0) {
        response += `### 解决方案 (${data.solutions.length} 个)\n\n`;
        data.solutions.forEach((solution, index) => {
            response += `#### ${index + 1}. ${solution.title}\n`;
            response += `**作者**: ${solution.author}\n`;
            response += `**验证状态**: ${solution.verified ? '✅ 已验证' : '⚠️ 未验证'}\n`;
            response += `**投票数**: ${solution.votes}\n`;
            response += `**来源**: ${solution.sourceUrl}\n`;
            response += `**描述**: ${solution.description}\n`;
            if (solution.tags && solution.tags.length > 0) {
                response += `**标签**: ${solution.tags.join(', ')}\n`;
            }
            response += '\n';
        });
    }
    if (data.discussions && data.discussions.length > 0) {
        response += `### 相关讨论 (${data.discussions.length} 个)\n\n`;
        data.discussions.forEach((discussion, index) => {
            response += `#### ${index + 1}. ${discussion.title}\n`;
            response += `**平台**: ${getPlatformLabel(discussion.platform)}\n`;
            response += `**回复数**: ${discussion.replies}\n`;
            response += `**最后活动**: ${new Date(discussion.lastActivity).toLocaleDateString()}\n`;
            response += `**链接**: ${discussion.url}\n\n`;
        });
    }
    if (data.relatedIssues && data.relatedIssues.length > 0) {
        response += `### 相关 Issues (${data.relatedIssues.length} 个)\n\n`;
        data.relatedIssues.forEach((issue, index) => {
            const stateIcon = issue.state === 'open' ? '🟢' : '🔴';
            response += `#### ${index + 1}. ${stateIcon} ${issue.title}\n`;
            response += `**编号**: #${issue.number}\n`;
            response += `**状态**: ${issue.state}\n`;
            response += `**创建时间**: ${new Date(issue.createdAt).toLocaleDateString()}\n`;
            response += `**链接**: ${issue.url}\n`;
            if (issue.labels && issue.labels.length > 0) {
                response += `**标签**: ${issue.labels.join(', ')}\n`;
            }
            response += '\n';
        });
    }
    return response;
}
/**
 * 辅助函数 - 获取严重性标签
 */
function getSeverityLabel(severity) {
    const labels = {
        'critical': '🚨 严重',
        'high': '🔴 高',
        'medium': '🟡 中',
        'low': '🟢 低'
    };
    return labels[severity] || severity;
}
/**
 * 辅助函数 - 获取影响标签
 */
function getImpactLabel(impact) {
    const labels = {
        'breaking': '💥 破坏性',
        'compatible': '✅ 兼容',
        'enhancement': '✨ 增强',
        'high': '🔴 高',
        'medium': '🟡 中',
        'low': '🟢 低'
    };
    return labels[impact] || impact;
}
/**
 * 辅助函数 - 获取变更类型标签
 */
function getChangeTypeLabel(type) {
    const labels = {
        'added': '✅ 新增',
        'modified': '🔄 修改',
        'deprecated': '⚠️ 弃用',
        'removed': '❌ 移除'
    };
    return labels[type] || type;
}
/**
 * 辅助函数 - 获取平台标签
 */
function getPlatformLabel(platform) {
    const labels = {
        'github': '🐙 GitHub',
        'discord': '💬 Discord',
        'reddit': '🤖 Reddit',
        'stackoverflow': '📚 Stack Overflow'
    };
    return labels[platform] || platform;
}
//# sourceMappingURL=responseFormatter.js.map