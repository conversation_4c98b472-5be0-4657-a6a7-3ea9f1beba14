#!/usr/bin/env node

/**
 * 测试 MCP 工具注册功能
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 测试 MCP 工具注册功能...\n');

// 测试工具列表请求
function testToolsRegistration() {
  return new Promise((resolve, reject) => {
    const serverPath = join(__dirname, 'dist', 'index.js');
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname
    });

    let output = '';
    let errorOutput = '';

    server.stdout.on('data', (data) => {
      output += data.toString();
    });

    server.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // 发送 ListTools 请求
    const listToolsRequest = {
      jsonrpc: "2.0",
      id: 1,
      method: "tools/list",
      params: {}
    };

    setTimeout(() => {
      server.stdin.write(JSON.stringify(listToolsRequest) + '\n');
    }, 2000);

    // 设置超时
    const timeout = setTimeout(() => {
      server.kill();
      resolve({
        success: false,
        error: 'Timeout',
        output,
        errorOutput
      });
    }, 10000);

    server.on('close', (code) => {
      clearTimeout(timeout);
      resolve({
        success: code === 0,
        code,
        output,
        errorOutput
      });
    });

    server.on('error', (error) => {
      clearTimeout(timeout);
      reject(error);
    });
  });
}

async function main() {
  try {
    console.log('📋 启动 MCP 服务器并测试工具注册...');
    
    const result = await testToolsRegistration();
    
    console.log('📊 测试结果:');
    console.log(`   退出码: ${result.code}`);
    console.log(`   成功: ${result.success ? '✅' : '❌'}`);
    
    if (result.output) {
      console.log('\n📄 服务器输出:');
      console.log(result.output);
    }
    
    if (result.errorOutput) {
      console.log('\n⚠️ 错误输出:');
      console.log(result.errorOutput);
    }

    // 检查是否包含预期的工具
    const expectedTools = [
      'search_axmol_documentation',
      'find_code_examples', 
      'get_api_reference',
      'solve_build_issue',
      'get_migration_guide',
      'find_platform_specific_info',
      'analyze_axmol_code',
      'compare_axmol_versions',
      'get_best_practices',
      'search_community_solutions'
    ];

    console.log('\n🔧 检查工具注册状态:');
    let toolsFound = 0;
    
    expectedTools.forEach(tool => {
      const found = result.output.includes(tool);
      console.log(`   ${found ? '✅' : '❌'} ${tool}`);
      if (found) toolsFound++;
    });

    console.log(`\n📊 工具注册统计: ${toolsFound}/${expectedTools.length} 个工具已注册`);
    
    if (toolsFound === expectedTools.length) {
      console.log('🎉 所有 MCP 工具都已正确注册！');
    } else {
      console.log('⚠️ 部分工具未正确注册');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

main();
