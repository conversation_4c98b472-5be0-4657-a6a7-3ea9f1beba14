{"version": 3, "file": "codeAnalysisService.js", "sourceRoot": "", "sources": ["../../src/services/codeAnalysisService.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,MAAM,OAAO,mBAAmB;IACb,SAAS,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS;IAEtD,UAAU;IACO,aAAa,GAAqB,IAAI,GAAG,EAAE,CAAC;IAC5C,eAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;IAE1E;QACE,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,IAAY,EACZ,QAAuB,EACvB,UAAyB,EAAE;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,WAAW,QAAQ,QAAQ,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC;YAE1D,QAAQ;YACR,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrC,MAAM,QAAQ,GAAG,iBAAiB,QAAQ,IAAI,QAAQ,EAAE,CAAC;YAEzD,UAAU;YACV,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;gBAC/B,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,QAAQ,CAA8B,CAAC;gBAC7E,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBAC7B,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE;4BACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BAClC,YAAY,EAAE,CAAC;4BACf,OAAO,EAAE,CAAC,OAAO,CAAC;4BAClB,QAAQ,EAAE,IAAI;yBACf;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GAAa,CAAC,iBAAiB,CAAC,CAAC;YAE9C,SAAS;YACT,MAAM,cAAc,GAAuB;gBACzC,IAAI;gBACJ,QAAQ;gBACR,MAAM,EAAE,EAAE;gBACV,WAAW,EAAE,EAAE;gBACf,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,QAAQ,EAAE,EAAE;aACb,CAAC;YAEF,YAAY;YACZ,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAEnE,YAAY;YACZ,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;YAE7F,YAAY;YACZ,cAAc,CAAC,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAEvE,UAAU;YACV,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAErE,UAAU;YACV,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAE7D,OAAO;YACP,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;gBAC/B,MAAM,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,MAAM,GAAG,cAAc,CAAC,WAAW,CAAC,MAAM,CAAC;YACrF,OAAO,CAAC,GAAG,CAAC,gBAAgB,WAAW,SAAS,CAAC,CAAC;YAElD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,cAAc;gBACpB,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAClC,YAAY,EAAE,WAAW;oBACzB,OAAO;oBACP,QAAQ,EAAE,KAAK;iBAChB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,kBAAkB,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;YAE/H,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAClC,YAAY,EAAE,CAAC;oBACf,OAAO,EAAE,EAAE;oBACX,QAAQ,EAAE,KAAK;iBAChB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,WAAW;QACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,EAAE;YACzC,OAAO,EAAE,wBAAwB;YACjC,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,gCAAgC;YACzC,UAAU,EAAE,iBAAiB;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,oBAAoB,EAAE;YAC3C,QAAQ,EAAE;gBACR,WAAW;gBACX,SAAS;gBACT,UAAU;gBACV,UAAU;aACX;YACD,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,uBAAuB;YAChC,UAAU,EAAE,4BAA4B;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAqB,EAAE;YAC5C,OAAO,EAAE,2BAA2B;YACpC,QAAQ,EAAE,KAAK;YACf,OAAO,EAAE,iCAAiC;YAC1C,UAAU,EAAE,oBAAoB;SACjC,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,EAAE;YAC7C,OAAO,EAAE,eAAe;YACxB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,oBAAoB;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,oBAAoB,EAAE;YAC3C,QAAQ,EAAE;gBACR,aAAa;gBACb,WAAW;gBACX,YAAY;aACb;YACD,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,2BAA2B;YACpC,UAAU,EAAE,sBAAsB;SACnC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,aAAa,CAAC,IAAI,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,WAAW;QACX,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,mBAAmB,EAAE;YAC5C;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,YAAY;gBACnB,WAAW,EAAE,0BAA0B;gBACvC,eAAe,EAAE;oBACf,sCAAsC;oBACtC,iBAAiB;oBACjB,kBAAkB;iBACnB;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,UAAU;wBACjB,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;YAMN;wBACA,WAAW,EAAE,kBAAkB;qBAChC;iBACF;gBACD,YAAY,EAAE;oBACZ,kBAAkB;oBAClB,WAAW;oBACX,YAAY;iBACb;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,MAAM;wBACd,cAAc,EAAE,YAAY;wBAC5B,MAAM,EAAE,MAAM;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,EAAE;YACtC;gBACE,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,UAAU;gBACjB,WAAW,EAAE,iBAAiB;gBAC9B,eAAe,EAAE;oBACf,2BAA2B;oBAC3B,eAAe;oBACf,UAAU;iBACX;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;;EAOhB;wBACU,WAAW,EAAE,iBAAiB;qBAC/B;iBACF;gBACD,YAAY,EAAE;oBACZ,aAAa;oBACb,WAAW;oBACX,UAAU;iBACX;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,MAAM;wBACd,cAAc,EAAE,UAAU;wBAC1B,MAAM,EAAE,MAAM;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,eAAe,CAAC,IAAI,UAAU,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,IAAY,EAAE,QAAuB;QACjE,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE/B,WAAW;QACX,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAAE,SAAS;YAE7C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,SAAS;gBACT,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;YACpD,CAAC;iBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzB,SAAS;gBACT,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACpC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,EAAE,MAAM,CAAC,CAAC;gBACpE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY,EAAE,KAAe,EAAE,IAAS,EAAE,MAAmB;QACrF,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,UAAU;gBACV,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAErD,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;oBAC1F,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;oBAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAe,EAAE,SAAiB;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,CAAC;YACf,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAY,EAAE,QAAuB,EAAE,MAAmB;QACpF,MAAM,WAAW,GAAqB,EAAE,CAAC;QAEzC,cAAc;QACd,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACnE,IAAI,UAAU,EAAE,CAAC;gBACf,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,WAAW;QACX,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QAErE,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,KAAgB,EAAE,QAAuB;QACzE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,uBAAuB;gBAChC,aAAa,EAAE,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,4CAA4C,CAAC,CAAC,CAAC,8CAA8C;gBACjI,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,MAAM;aACjB,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,kBAAkB;gBAC3B,aAAa,EAAE,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc;gBACnE,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,QAAQ;aACnB,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,IAAY,EAAE,QAAuB;QACtE,MAAM,WAAW,GAAqB,EAAE,CAAC;QAEzC,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;YACvB,WAAW;YACX,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;gBACzC,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,CAAC;oBACP,OAAO,EAAE,iBAAiB;oBAC1B,aAAa,EAAE,oBAAoB;oBACnC,MAAM,EAAE,mBAAmB;oBAC3B,QAAQ,EAAE,KAAK;iBAChB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAClC,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,CAAC;oBACP,OAAO,EAAE,iBAAiB;oBAC1B,aAAa,EAAE,cAAc;oBAC7B,MAAM,EAAE,iBAAiB;oBACzB,QAAQ,EAAE,MAAM;iBACjB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAY,EAAE,QAAuB;QAC9D,MAAM,SAAS,GAAmB,EAAE,CAAC;QAErC,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtD,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YACtE,IAAI,eAAe,EAAE,CAAC;gBACpB,SAAS,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,MAAM,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACrE,IAAI,oBAAoB,EAAE,CAAC;gBACzB,SAAS,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAY,EAAE,QAAuB;QAC9D,MAAM,KAAK,GAAsB,EAAE,CAAC;QAEpC,aAAa;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE,CAAC;YAChD,KAAK,CAAC,IAAI,CAAC;gBACT,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,sBAAsB;gBACtC,MAAM,EAAE,MAAM;gBACd,WAAW,EAAE,gBAAgB;aAC9B,CAAC,CAAC;QACL,CAAC;QAED,SAAS;QACT,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACzE,KAAK,CAAC,IAAI,CAAC;gBACT,MAAM,EAAE,MAAM;gBACd,cAAc,EAAE,kBAAkB;gBAClC,MAAM,EAAE,QAAQ;gBAChB,WAAW,EAAE,aAAa;aAC3B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,IAAY,EAAE,QAAuB;QACzD,MAAM,KAAK,GAAmB,EAAE,CAAC;QAEjC,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,cAAc;gBAC3B,QAAQ,EAAE,QAAQ;gBAClB,cAAc,EAAE,qBAAqB;aACtC,CAAC,CAAC;QACL,CAAC;QAED,UAAU;QACV,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC;gBACT,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,cAAc;gBAC3B,QAAQ,EAAE,MAAM;gBAChB,cAAc,EAAE,mCAAmC;aACpD,CAAC,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,IAAY;QAC3B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YACnC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,WAAW;QACjC,CAAC;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACrC,CAAC;CACF;AAED,eAAe;AACf,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC"}