{"data": {"fromEngine": "Cocos2d-x 3.x", "toEngine": "Axmol", "topic": "general", "changes": [{"category": "命名空间", "oldApi": "cocos2d::", "newApi": "ax::", "description": "所有Cocos2d-x的命名空间都改为ax", "breaking": true, "migrationSteps": ["全局替换 \"cocos2d::\" 为 \"ax::\"", "更新所有头文件包含路径", "检查自定义类的继承关系"]}, {"category": "头文件", "oldApi": "#include \"cocos2d.h\"", "newApi": "#include \"axmol.h\"", "description": "主头文件名称变更", "breaking": true, "migrationSteps": ["替换主头文件包含", "更新其他相关头文件路径", "检查预编译头文件配置"]}, {"category": "Director", "oldApi": "Director::get<PERSON><PERSON><PERSON>()", "newApi": "Director::get<PERSON><PERSON><PERSON>()", "description": "Director API保持兼容，但命名空间变更", "breaking": false, "migrationSteps": ["更新命名空间引用", "检查Director相关的自定义代码"]}], "examples": [{"title": "基础场景创建迁移示例", "language": "cpp", "code": "// Cocos2d-x 3.x\n#include \"cocos2d.h\"\nUSING_NS_CC;\n\nclass HelloWorld : public cocos2d::Scene\n{\npublic:\n    static cocos2d::Scene* createScene();\n    virtual bool init();\n    CREATE_FUNC(HelloWorld);\n};\n\n// Axmol\n#include \"axmol.h\"\nUSING_NS_AX;\n\nclass HelloWorld : public ax::Scene\n{\npublic:\n    static ax::Scene* createScene();\n    virtual bool init();\n    CREATE_FUNC(HelloWorld);\n};", "description": "展示基础的命名空间和头文件迁移"}], "commonIssues": [{"platform": "all", "errorMessage": "编译错误：找不到头文件", "errorType": "compile", "solutions": [{"title": "更新头文件路径", "description": "迁移后需要更新所有头文件的包含路径", "steps": ["检查所有 #include 语句", "将 Cocos2d-x 头文件路径替换为 Axmol 路径", "更新项目配置中的头文件搜索路径"], "codeChanges": [], "configChanges": [], "priority": "high", "verified": true}], "relatedIssues": []}], "resources": [{"type": "official_docs", "title": "Axmol GitHub 仓库", "url": "https://github.com/axmolengine/axmol", "content": "Axmol 引擎的官方 GitHub 仓库", "relevanceScore": 10, "matchedTerms": ["axmol", "migration"], "source": "official"}, {"type": "wiki", "title": "Axmol Wiki", "url": "https://github.com/axmolengine/axmol/wiki", "content": "Axmol 引擎的官方 Wiki 文档", "relevanceScore": 9, "matchedTerms": ["wiki", "documentation"], "source": "official"}]}, "timestamp": 1750515564305, "ttl": 14400000, "key": "migration_cocos2d-x-3.x_general_{}"}