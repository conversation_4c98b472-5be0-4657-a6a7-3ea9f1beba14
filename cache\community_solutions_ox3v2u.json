{"data": {"problem": "sprite rendering issue", "solutions": [{"title": "GitHub Issue 解决方案: `Sprite::setPolygonInfo()` doesn't set content size", "author": "smilediver", "description": "`Sprite::setPolygonInfo()` doesn't call `setContentSize()`, while `Sprite::initWithPolygon()` does. It looks like `Sprite::setPolygonInfo()` should also call it, but I'm not 100% certain so would like some external input.\r\n\r\nhttps://github.com/axmolengine/axmol/blob/284fcf243a5d81862c7cca623ec1a0fbf7e8304e/core/2d/Sprite.cpp#L1715-L1719\r\n\r\nhttps://github.com/axmolengine/axmol/blob/284fcf243a5d81862c7cca623ec1a0fbf7e8304e/core/2d/Sprite.cpp#L260-L274", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/2112", "tags": []}, {"title": "GitHub Issue 解决方案: Occlusion Culling Support", "author": "anishkumar0712", "description": "We have noticed that Axmol makes draw calls for nodes fully occluded by others, impacting performance. \n\nFor example, when a Layer A(covering full screen) is rendered on top of another Layer B, draw calls are still made for Layer B, even though it's not visible to the user. We specifically need to call setVisible(false) to stop draw calls for those layers.\n\nDoes the engine support occlusion culling to skip rendering these layers? ", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/2362", "tags": []}, {"title": "GitHub Issue 解决方案: CommandBufferGL::drawElements Crash", "author": "kiranb47", "description": "- axmol version: Latest dev branch\r\n- devices test on:\r\n- developing environments\r\n   - NDK version: r19c\r\n   - Xcode version: 12.4\r\n   - Visual Studio: \r\n      - VS version: 2019 (16.11), 2022 (17.4)\r\n      - MSVC version: 1929, 1934\r\n      - Windows SDK version: 10.0.22621.0\r\n   - cmake version: \r\nSteps to Reproduce:\r\n\r\nWe have migrated our Match Animal game to Axmol from Cocos2dx V3 latest yesterday. Getting this crash now. How to fix this issue?\r\n\r\nbacktrace:\r\n  #00  pc 0x00000000005ffa58  /", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/1211", "tags": ["wontfix", "HelpDesk"]}, {"title": "GitHub Issue 解决方案: Cannot Capture a ScrollView: Issue with RenderTexture?", "author": "as<PERSON><PERSON>", "description": "Hi guys,\r\n\tI created a method/function to help capture a ScrollView But when I try to capture a a ScrollView using the method/function I get an empty png image. I’m not sure if there is an issue with the Axmol RenderTexture or if I’m doing something wrong. Some help will be appreciated.\r\n\tPerhaps there is already a method in Axmol to help do that? If yes please let me know so I don’t reinvent the wheel.\r\n        \r\n\r\n**This is the function I'm using to capture a a ScrollView** \r\n\r\n    void CModal", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/2002", "tags": []}, {"title": "GitHub Issue 解决方案: Cannot capture a layout: issue with RenderTexture?", "author": "as<PERSON><PERSON>", "description": "Hi guys,\r\n\tI created a method/function to help capture a Layout. But when I try to capture a layout using the method/function I get an empty png image. I’m not sure if there is an issue with the Axmol RenderTexture or if I’m doing something wrong. Some help will be appreciated.\r\n\tPerhaps there is already a method in Axmol to help do that? If yes please let me know so I don’t reinvent the wheel.\r\n        \r\n\r\n**This is the function I'm using to capture a layout** \r\n\r\n     void captureLayout(Node* ", "code": [], "votes": 0, "verified": true, "sourceUrl": "https://github.com/axmolengine/axmol/issues/2001", "tags": ["duplicate"]}], "discussions": [{"title": "社区讨论：相关问题解决方案", "url": "https://github.com/axmolengine/axmol/discussions", "platform": "github", "replies": 0, "lastActivity": "2025-06-21T14:21:01.359Z", "tags": ["community", "discussion"]}], "relatedIssues": [{"number": 966, "title": "Some RenderTexture issue", "state": "closed", "labels": ["bug"], "url": "https://github.com/axmolengine/axmol/issues/966", "createdAt": "2022-11-21T08:40:11Z", "updatedAt": "2022-11-29T09:09:04Z"}, {"number": 879, "title": "Sprite3D does not handle model with transparency", "state": "closed", "labels": ["bug", "cocos2dx"], "url": "https://github.com/axmolengine/axmol/issues/879", "createdAt": "2022-10-02T07:13:55Z", "updatedAt": "2022-10-07T18:13:19Z"}, {"number": 2112, "title": "`Sprite::setPolygonInfo()` doesn't set content size", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/2112", "createdAt": "2024-08-28T13:03:56Z", "updatedAt": "2024-08-28T15:13:42Z"}, {"number": 2002, "title": "Cannot Capture a ScrollView: Issue with RenderTexture?", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/2002", "createdAt": "2024-06-25T11:21:30Z", "updatedAt": "2024-06-25T18:02:35Z"}, {"number": 2001, "title": "Cannot capture a layout: issue with RenderTexture?", "state": "closed", "labels": ["duplicate"], "url": "https://github.com/axmolengine/axmol/issues/2001", "createdAt": "2024-06-25T11:17:05Z", "updatedAt": "2024-06-25T17:04:35Z"}, {"number": 1552, "title": "Rendering artifacts", "state": "closed", "labels": ["documentation", "HelpDesk"], "url": "https://github.com/axmolengine/axmol/issues/1552", "createdAt": "2023-12-28T16:39:53Z", "updatedAt": "2024-01-12T09:28:57Z"}, {"number": 1121, "title": "Performance issue vs Cocos2d-x v3.17", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/1121", "createdAt": "2023-03-15T06:39:18Z", "updatedAt": "2023-03-22T01:19:29Z"}, {"number": 1094, "title": "iOS ClippingNode performance issue?", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/1094", "createdAt": "2023-03-03T07:56:16Z", "updatedAt": "2023-03-15T03:20:12Z"}, {"number": 688, "title": "[PROPOSAL] Change class name `Sprite3D` to `<PERSON>sh<PERSON><PERSON><PERSON>`", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/688", "createdAt": "2022-06-30T22:32:14Z", "updatedAt": "2022-07-09T21:04:05Z"}, {"number": 697, "title": "[BUG] Angle `D3D11` `glEnableVertexAttribArray()` program state rendering error.", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/697", "createdAt": "2022-07-01T22:47:11Z", "updatedAt": "2022-07-02T16:47:21Z"}, {"number": 2362, "title": "Occlusion Culling Support", "state": "closed", "labels": [], "url": "https://github.com/axmolengine/axmol/issues/2362", "createdAt": "2025-02-03T06:20:30Z", "updatedAt": "2025-02-11T06:28:56Z"}, {"number": 1211, "title": "CommandBufferGL::drawElements Crash", "state": "closed", "labels": ["wontfix", "HelpDesk"], "url": "https://github.com/axmolengine/axmol/issues/1211", "createdAt": "2023-05-24T17:10:16Z", "updatedAt": "2024-11-24T20:52:16Z"}, {"number": 1043, "title": "Instancing proposal", "state": "closed", "labels": ["feature proposal"], "url": "https://github.com/axmolengine/axmol/issues/1043", "createdAt": "2023-02-04T08:32:54Z", "updatedAt": "2023-07-29T06:29:12Z"}, {"number": 678, "title": "RenderTarget | render to texture", "state": "closed", "labels": ["HelpDesk"], "url": "https://github.com/axmolengine/axmol/issues/678", "createdAt": "2022-06-29T16:42:42Z", "updatedAt": "2022-08-13T18:09:59Z"}, {"number": 482, "title": "Updated shader uniforms not rendered properly on Spine nodes", "state": "closed", "labels": ["HelpDesk", "spine-runtime"], "url": "https://github.com/axmolengine/axmol/issues/482", "createdAt": "2021-08-29T06:33:03Z", "updatedAt": "2021-09-14T04:49:05Z"}]}, "timestamp": 1750515661924, "ttl": 3600000, "key": "community_solutions_ox3v2u"}