{"data": [{"useCase": "performance", "category": "性能优化", "title": "渲染性能优化", "description": "通过批量渲染、纹理优化等技术提升游戏性能", "recommendations": ["使用 SpriteBatchNode 进行批量渲染", "合理使用纹理图集减少绘制调用", "实现对象池避免频繁内存分配", "使用 LOD (Level of Detail) 技术", "优化着色器和材质使用"], "examples": [{"title": "对象池实现", "language": "cpp", "code": "template<typename T>\nclass ObjectPool {\npublic:\n    T* acquire() {\n        if (available.empty()) {\n            return new T();\n        }\n        T* obj = available.back();\n        available.pop_back();\n        return obj;\n    }\n    \n    void release(T* obj) {\n        obj->reset();\n        available.push_back(obj);\n    }\n    \nprivate:\n    std::vector<T*> available;\n};\n\n// 使用示例\nauto bulletPool = ObjectPool<Bullet>();\nauto bullet = bulletPool.acquire();", "description": "对象池减少内存分配开销"}], "antiPatterns": ["每帧创建和销毁大量对象", "使用过多的绘制调用", "不合理的纹理大小和格式"], "performance": [{"aspect": "帧率", "recommendation": "减少绘制调用和内存分配", "impact": "high", "measurement": "FPS 提升 30-80%"}], "resources": [{"type": "official_docs", "title": "Axmol 官方文档", "url": "https://axmol.dev/manual/latest", "content": "Axmol 引擎的官方文档", "relevanceScore": 8, "matchedTerms": ["performance", "性能优化"], "source": "official"}, {"type": "wiki", "title": "Axmol GitHub Wiki", "url": "https://github.com/axmolengine/axmol/wiki", "content": "Axmol 引擎的 GitHub Wiki", "relevanceScore": 7, "matchedTerms": ["performance"], "source": "community"}]}], "timestamp": 1750515276262, "ttl": 7200000, "key": "best_practices_performance_{}"}