{"data": [{"title": "sprite 示例 - AnchoredSprite", "language": "cpp", "code": "all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONI<PERSON><PERSON>NGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n****************************************************************************/\n#include \"2d/AnchoredSprite.h\"\n#include \"renderer/backend/DriverBase.h\"\n\nnamespace ax\n{\n\n// FIXME: HACK: optimization\n#define SET_DIRTY_RECURSIVELY()            \\\n    {                                      \\\n        if (!_recursiveDirty)              \\\n        {                                  \\\n\n// ...\n\n        if (!_recursiveDirty)              \\\n        {                                  \\\n            _recursiveDirty = true;        \\\n            setDirty(true);                \\\n            if (!_children.empty())        \\\n                setDirtyRecursively(true); \\\n        }                                  \\\n    }\n\n// MARK: create, init, dealloc\nAnchoredSprite* AnchoredSprite::createWithTexture(Texture2D* texture)\n{\n    AnchoredSprite* asprite = new AnchoredSprite();\n    if (asprite->initWithTexture(texture))\n    {\n        asprite->autorelease();\n        return asprite;\n    }\n    AX_SAFE_DELETE(asprite);\n    return nullptr;\n}\n\n// ...\n\n    AnchoredSprite* asprite = new AnchoredSprite();\n    if (asprite->initWithTexture(texture))\n    {\n        asprite->autorelease();\n        return asprite;\n    }\n    AX_SAFE_DELETE(asprite);\n    return nullptr;\n}\n\nAnchoredSprite* AnchoredSprite::createWithTexture(Texture2D* texture, const Rect& rect, bool rotated)\n{\n    AnchoredSprite* asprite = new AnchoredSprite();\n    if (asprite->initWithTexture(texture, rect, rotated))\n    {\n        asprite->autorelease();\n        return asprite;\n    }\n    AX_SAFE_DELETE(asprite);\n    return nullptr;\n}", "description": "从 AnchoredSprite.cpp 中提取的 sprite 相关代码示例，包含 3 个代码段。", "platform": "cross-platform", "version": "latest", "sourceUrl": "https://github.com/axmolengine/axmol/blob/dev/core/2d/AnchoredSprite.cpp"}, {"title": "sprite 示例 - AnchoredSprite", "language": "cpp", "code": "IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPY<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, <PERSON><PERSON><PERSON>ES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n****************************************************************************/\n#pragma once\n\n#include <string>\n#include \"2d/Sprite.h\"\n\nnamespace ax\n{\n\n/**\n * @addtogroup _2d\n * @{\n */\n\n/**\n\n// ...\n\n{\n\n/**\n * @addtogroup _2d\n * @{\n */\n\n/**\n * Anchored Sprite\n *\n * This node follows the same rules as the normal Sprite.\n * but instead of anchoring the node with its children,\n * it instead moves the vertices and anchors them without\n * altering the node's children positions\n *\n * The default anchorPoint of Anchored Sprite is (0.5, 0.5).\n */\nclass AX_DLL AnchoredSprite : public Sprite\n{\npublic:\n    /// @name Creators\n\n// ...\n\n *\n * The default anchorPoint of Anchored Sprite is (0.5, 0.5).\n */\nclass AX_DLL AnchoredSprite : public Sprite\n{\npublic:\n    /// @name Creators\n    /// @{\n\n    /**\n     * Creates an empty anchored sprite without texture. You can call setTexture method subsequently.\n     *\n     * @memberof Sprite\n     * @return An autoreleased sprite object.\n     */\n    static AnchoredSprite* create();\n\n    /**\n     * Creates an anchored sprite with an image filename.\n     *\n     * After creation, the rect of sprite will be the size of the image,", "description": "从 AnchoredSprite.h 中提取的 sprite 相关代码示例，包含 3 个代码段。", "platform": "cross-platform", "version": "latest", "sourceUrl": "https://github.com/axmolengine/axmol/blob/dev/core/2d/AnchoredSprite.h"}], "timestamp": 1750515557256, "ttl": 3600000, "key": "code_examples_sprite_all_{\"language\":\"cpp\",\"maxResults\":2}"}