{"version": 3, "file": "bestPracticesService.js", "sourceRoot": "", "sources": ["../../src/services/bestPracticesService.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAIH,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,MAAM,OAAO,oBAAoB;IACd,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,QAAQ;IAEzD,UAAU;IACO,WAAW,GAAgC,IAAI,GAAG,EAAE,CAAC;IAEtE;QACE,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CACpB,OAAe,EACf,UAAyB,EAAE;QAE3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;YAErC,QAAQ;YACR,MAAM,QAAQ,GAAG,kBAAkB,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;YAExE,UAAU;YACV,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;gBAC/B,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,QAAQ,CAA0B,CAAC;gBACzE,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBAC3B,OAAO;wBACL,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE;4BACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;4BAClC,YAAY,EAAE,MAAM,CAAC,MAAM;4BAC3B,OAAO,EAAE,CAAC,OAAO,CAAC;4BAClB,QAAQ,EAAE,IAAI;yBACf;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GAAa,EAAE,CAAC;YAC7B,IAAI,SAAS,GAAmB,EAAE,CAAC;YAEnC,eAAe;YACf,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YACjD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACpC,CAAC;YAED,cAAc;YACd,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;YACxE,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,SAAS,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;gBACtC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,CAAC;YAED,YAAY;YACZ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3B,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;gBACnD,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,CAAC;YAED,SAAS;YACT,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE5D,SAAS;YACT,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAE9D,OAAO;YACP,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvD,MAAM,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,SAAS,CAAC,MAAM,MAAM,CAAC,CAAC;YAEtD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAClC,YAAY,EAAE,SAAS,CAAC,MAAM;oBAC9B,OAAO;oBACP,QAAQ,EAAE,KAAK;iBAChB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,YAAY,CAAC,cAAc,CAAC,KAAK,EAAE,kBAAkB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAEhG,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,UAAU;gBACjB,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;oBAClC,YAAY,EAAE,CAAC;oBACf,OAAO,EAAE,EAAE;oBACX,QAAQ,EAAE,KAAK;iBAChB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,WAAW;QACX,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,mBAAmB,EAAE;YACxC;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,cAAc;gBACrB,WAAW,EAAE,+BAA+B;gBAC5C,eAAe,EAAE;oBACf,YAAY;oBACZ,iBAAiB;oBACjB,aAAa;oBACb,YAAY;iBACb;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,SAAS;wBAChB,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;;;;;;;;;mDAciC;wBACvC,WAAW,EAAE,mBAAmB;qBACjC;iBACF;gBACD,YAAY,EAAE;oBACZ,cAAc;oBACd,cAAc;oBACd,UAAU;iBACX;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,OAAO;wBACf,cAAc,EAAE,aAAa;wBAC7B,MAAM,EAAE,MAAM;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE;YAClC;gBACE,OAAO,EAAE,aAAa;gBACtB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,QAAQ;gBACf,WAAW,EAAE,sBAAsB;gBACnC,eAAe,EAAE;oBACf,2BAA2B;oBAC3B,gBAAgB;oBAChB,eAAe;oBACf,6BAA6B;oBAC7B,YAAY;iBACb;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;oCAuBkB;wBACxB,WAAW,EAAE,aAAa;qBAC3B;iBACF;gBACD,YAAY,EAAE;oBACZ,aAAa;oBACb,WAAW;oBACX,aAAa;iBACd;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,IAAI;wBACZ,cAAc,EAAE,aAAa;wBAC7B,MAAM,EAAE,MAAM;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,mBAAmB,EAAE;YACxC;gBACE,OAAO,EAAE,mBAAmB;gBAC5B,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,QAAQ;gBACf,WAAW,EAAE,uBAAuB;gBACpC,eAAe,EAAE;oBACf,wBAAwB;oBACxB,wBAAwB;oBACxB,QAAQ;oBACR,UAAU;oBACV,aAAa;iBACd;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,SAAS;wBAChB,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;;;;;;;GAYf;wBACS,WAAW,EAAE,wBAAwB;qBACtC;iBACF;gBACD,YAAY,EAAE;oBACZ,kBAAkB;oBAClB,aAAa;oBACb,QAAQ;iBACT;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,MAAM;wBACd,cAAc,EAAE,YAAY;wBAC5B,MAAM,EAAE,MAAM;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,qBAAqB,EAAE;YAC1C;gBACE,OAAO,EAAE,qBAAqB;gBAC9B,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAE,UAAU;gBACjB,WAAW,EAAE,eAAe;gBAC5B,eAAe,EAAE;oBACf,uBAAuB;oBACvB,WAAW;oBACX,aAAa;oBACb,YAAY;oBACZ,aAAa;iBACd;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;;OAOX;wBACK,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,YAAY,EAAE;oBACZ,aAAa;oBACb,UAAU;oBACV,UAAU;iBACX;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,MAAM;wBACd,cAAc,EAAE,aAAa;wBAC7B,MAAM,EAAE,MAAM;wBACd,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,QAAQ;QACR,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACrC;gBACE,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,OAAO;gBACjB,KAAK,EAAE,YAAY;gBACnB,WAAW,EAAE,mBAAmB;gBAChC,eAAe,EAAE;oBACf,aAAa;oBACb,cAAc;oBACd,WAAW;oBACX,aAAa;oBACb,WAAW;iBACZ;gBACD,QAAQ,EAAE;oBACR;wBACE,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE;;;;;;;;;;;;;;GAcf;wBACS,WAAW,EAAE,eAAe;qBAC7B;iBACF;gBACD,YAAY,EAAE;oBACZ,WAAW;oBACX,aAAa;oBACb,mBAAmB;iBACpB;gBACD,WAAW,EAAE;oBACX;wBACE,MAAM,EAAE,KAAK;wBACb,cAAc,EAAE,cAAc;wBAC9B,MAAM,EAAE,QAAQ;wBAChB,WAAW,EAAE,cAAc;qBAC5B;iBACF;gBACD,SAAS,EAAE,EAAE;aACd;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAe;QAC5C,MAAM,SAAS,GAAmB,EAAE,CAAC;QACrC,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,OAAO;QACP,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,UAAU,EAAE,CAAC;YACf,SAAS,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QAChC,CAAC;QAED,OAAO;QACP,KAAK,MAAM,CAAC,GAAG,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,IAAI,GAAG,KAAK,YAAY,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBACvF,SAAS,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,QAAQ;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACtD,KAAK,MAAM,CAAC,GAAG,EAAE,iBAAiB,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBACpD,SAAS,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC7C,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC,CACxD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,OAAe;QAC5C,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,UAAU;QACV,IAAI,YAAY,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC;YAC7E,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACnE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/B,CAAC;QAED,UAAU;QACV,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;YAChE,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACxC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QAED,UAAU;QACV,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACxE,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAChC,CAAC;QAED,UAAU;QACV,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;YACnE,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1B,CAAC;QAED,SAAS;QACT,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACnE,YAAY,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CAAC,OAAe;QACpD,wBAAwB;QACxB,iBAAiB;QACjB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,OAAe;QAC9C,MAAM,SAAS,GAAmB,EAAE,CAAC;QAErC,aAAa;QACb,SAAS,CAAC,IAAI,CAAC;YACb,OAAO;YACP,QAAQ,EAAE,MAAM;YAChB,KAAK,EAAE,GAAG,OAAO,SAAS;YAC1B,WAAW,EAAE,MAAM,OAAO,eAAe;YACzC,eAAe,EAAE;gBACf,gBAAgB;gBAChB,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,WAAW;aACZ;YACD,QAAQ,EAAE;gBACR;oBACE,KAAK,EAAE,QAAQ;oBACf,QAAQ,EAAE,KAAK;oBACf,IAAI,EAAE;QACR,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;;aAEnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;;;;;;;;;;;;GAYlC;oBACO,WAAW,EAAE,GAAG,OAAO,YAAY;iBACpC;aACF;YACD,YAAY,EAAE;gBACZ,QAAQ;gBACR,QAAQ;gBACR,SAAS;aACV;YACD,WAAW,EAAE;gBACX;oBACE,MAAM,EAAE,MAAM;oBACd,cAAc,EAAE,cAAc;oBAC9B,MAAM,EAAE,QAAQ;oBAChB,WAAW,EAAE,kBAAkB;iBAChC;aACF;YACD,SAAS,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,GAAW;QAC5B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,SAAyB,EAAE,OAAe;QACvE,cAAc;QACd,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,QAAQ,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,QAAsB,EAAE,OAAe;QACxE,MAAM,SAAS,GAAoB;YACjC;gBACE,IAAI,EAAE,eAAe;gBACrB,KAAK,EAAE,YAAY;gBACnB,GAAG,EAAE,iCAAiC;gBACtC,OAAO,EAAE,eAAe;gBACxB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC;gBAC1C,MAAM,EAAE,UAAU;aACnB;YACD;gBACE,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,mBAAmB;gBAC1B,GAAG,EAAE,2CAA2C;gBAChD,OAAO,EAAE,uBAAuB;gBAChC,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC,OAAO,CAAC;gBACvB,MAAM,EAAE,WAAW;aACpB;SACF,CAAC;QAEF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,SAAyB,EAAE,OAAe;QACzE,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC7B,UAAU;YACV,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YAExD,OAAO,MAAM,GAAG,MAAM,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,QAAsB,EAAE,OAAe;QACrE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAE3C,SAAS;QACT,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,YAAY,EAAE,CAAC;YACpD,KAAK,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACjE,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,OAAO;QACP,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,OAAO;QACP,IAAI,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9D,KAAK,IAAI,CAAC,CAAC;QACb,CAAC;QAED,SAAS;QACT,MAAM,eAAe,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QACrF,KAAK,IAAI,eAAe,GAAG,CAAC,CAAC;QAE7B,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED,eAAe;AACf,MAAM,CAAC,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAC"}