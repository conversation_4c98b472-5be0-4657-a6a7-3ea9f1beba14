{"data": {"platform": "Android", "topic": "build", "requirements": ["Android Studio 4.0 或更高版本", "Android NDK r23c 或更高版本", "Android SDK API Level 21 或更高版本", "CMake 3.16 或更高版本", "Java 8 或更高版本"], "configuration": [{"file": "local.properties", "setting": "ndk.dir", "value": "/path/to/android-ndk-r23c", "explanation": "设置 Android NDK 路径"}, {"file": "local.properties", "setting": "sdk.dir", "value": "/path/to/android-sdk", "explanation": "设置 Android SDK 路径"}, {"file": "app/build.gradle", "setting": "minSdkVersion", "value": "21", "explanation": "设置最低支持的 Android API 级别"}], "buildSteps": ["确保已安装所有必需的工具", "配置 local.properties 文件", "运行 ./gradlew assembleDebug 构建调试版本", "或运行 ./gradlew assembleRelease 构建发布版本"], "commonIssues": [{"platform": "Android", "errorMessage": "NDK not found", "errorType": "configuration", "solutions": [{"title": "配置 Android NDK", "description": "正确配置 Android NDK 路径", "steps": ["下载并安装 Android NDK", "在 local.properties 中设置 ndk.dir", "重新同步项目"], "codeChanges": [], "configChanges": [], "priority": "high", "verified": true}], "relatedIssues": []}], "examples": [{"title": "Android 构建脚本示例", "language": "cpp", "code": "// Android 平台特定的构建配置示例\n#ifdef AX_PLATFORM_ANDROID\n    // Android 特定的初始化代码\n    // ...\n#endif", "description": "Android 平台的构建配置示例"}], "resources": []}, "timestamp": 1750515257363, "ttl": 7200000, "key": "platform_android_build_{}"}