#!/usr/bin/env node
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { CallToolRequestSchema, ListToolsRequestSchema, } from "@modelcontextprotocol/sdk/types.js";
// 导入所有服务模块
import { documentationService } from './services/documentationService.js';
import { codeExampleService } from './services/codeExampleService.js';
import { apiReferenceService } from './services/apiReferenceService.js';
import { buildIssueService } from './services/buildIssueService.js';
import { migrationService } from './services/migrationService.js';
import { platformService } from './services/platformService.js';
import { codeAnalysisService } from './services/codeAnalysisService.js';
import { versionCompareService } from './services/versionCompareService.js';
import { bestPracticesService } from './services/bestPracticesService.js';
import { communityService } from './services/communityService.js';
// 导入工具类
import { errorHandler } from './utils/errorHandler.js';
import { dataSourceManager } from './utils/dataSourceManager.js';
import { formatDocumentationResponse, formatCodeExamplesResponse, formatApiReferenceResponse, formatBuildIssueResponse, formatMigrationGuideResponse, formatPlatformInfoResponse, formatCodeAnalysisResponse, formatVersionComparisonResponse, formatBestPracticesResponse, formatCommunityResponse } from './utils/responseFormatter.js';
// 创建服务器实例
const server = new Server({
    name: "axmol-mcp-server",
    version: "1.0.0",
});
// 注册所有 Axmol MCP 工具
server.setRequestHandler(ListToolsRequestSchema, async () => {
    return {
        tools: [
            // 第一阶段：核心工具
            {
                name: "search_axmol_documentation",
                description: "搜索 Axmol 官方文档、Wiki 和 API 参考。优先搜索官方知识库 (axmol.dev)，然后是 GitHub Wiki，最后是网络资源。",
                inputSchema: {
                    type: "object",
                    properties: {
                        query: {
                            type: "string",
                            description: "搜索查询，支持中文和英文关键词"
                        },
                        sourceType: {
                            type: "string",
                            enum: ["all", "official", "wiki", "api"],
                            description: "搜索的文档类型",
                            default: "all"
                        },
                        maxResults: {
                            type: "number",
                            description: "最大返回结果数量",
                            default: 20
                        },
                        useCache: {
                            type: "boolean",
                            description: "是否使用缓存",
                            default: true
                        }
                    },
                    required: ["query"]
                }
            },
            {
                name: "find_code_examples",
                description: "查找 Axmol 相关的代码示例，包括官方示例、测试代码和源码示例。支持按平台和语言筛选。",
                inputSchema: {
                    type: "object",
                    properties: {
                        feature: {
                            type: "string",
                            description: "要查找示例的功能或特性"
                        },
                        platform: {
                            type: "string",
                            enum: ["all", "android", "ios", "windows", "mac", "linux", "web"],
                            description: "目标平台",
                            default: "all"
                        },
                        language: {
                            type: "string",
                            enum: ["cpp", "lua", "both"],
                            description: "代码语言",
                            default: "cpp"
                        },
                        maxResults: {
                            type: "number",
                            description: "最大返回结果数量",
                            default: 15
                        }
                    },
                    required: ["feature"]
                }
            },
            {
                name: "get_api_reference",
                description: "获取 Axmol API 参考文档，包括类定义、方法签名、参数说明和使用示例。",
                inputSchema: {
                    type: "object",
                    properties: {
                        className: {
                            type: "string",
                            description: "要查询的类名"
                        },
                        methodName: {
                            type: "string",
                            description: "可选的方法名，用于查询特定方法"
                        },
                        includeExamples: {
                            type: "boolean",
                            description: "是否包含使用示例",
                            default: true
                        }
                    },
                    required: ["className"]
                }
            },
            {
                name: "solve_build_issue",
                description: "诊断和解决 Axmol 构建问题。分析错误信息并提供解决方案，支持多平台构建问题。",
                inputSchema: {
                    type: "object",
                    properties: {
                        platform: {
                            type: "string",
                            enum: ["android", "ios", "windows", "mac", "linux", "web"],
                            description: "构建平台"
                        },
                        errorMessage: {
                            type: "string",
                            description: "构建错误信息"
                        },
                        buildTool: {
                            type: "string",
                            enum: ["cmake", "gradle", "xcode", "visual_studio", "make"],
                            description: "使用的构建工具",
                            default: "cmake"
                        }
                    },
                    required: ["platform", "errorMessage"]
                }
            },
            // 第二阶段：增强工具
            {
                name: "get_migration_guide",
                description: "获取从 Cocos2d-x 到 Axmol 的迁移指南，包括 API 变更、代码示例和常见问题。",
                inputSchema: {
                    type: "object",
                    properties: {
                        fromEngine: {
                            type: "string",
                            enum: ["cocos2d-x-3.x", "cocos2d-x-4.x", "other"],
                            description: "源引擎版本",
                            default: "cocos2d-x-3.x"
                        },
                        topic: {
                            type: "string",
                            description: "迁移主题（如 'sprite', 'animation', 'physics' 等）",
                            default: "general"
                        },
                        includeExamples: {
                            type: "boolean",
                            description: "是否包含代码示例",
                            default: true
                        }
                    },
                    required: ["fromEngine"]
                }
            },
            {
                name: "find_platform_specific_info",
                description: "获取特定平台的配置、构建和开发信息。包括平台要求、配置步骤和常见问题。",
                inputSchema: {
                    type: "object",
                    properties: {
                        platform: {
                            type: "string",
                            enum: ["android", "ios", "windows", "mac", "linux", "web"],
                            description: "目标平台"
                        },
                        topic: {
                            type: "string",
                            enum: ["build", "configuration", "deployment", "debugging", "performance"],
                            description: "查询主题",
                            default: "build"
                        },
                        includeExamples: {
                            type: "boolean",
                            description: "是否包含配置示例",
                            default: true
                        }
                    },
                    required: ["platform"]
                }
            },
            // 第三阶段：高级工具
            {
                name: "analyze_axmol_code",
                description: "分析 Axmol 代码并提供最佳实践建议、性能优化和安全检查。",
                inputSchema: {
                    type: "object",
                    properties: {
                        code: {
                            type: "string",
                            description: "要分析的代码"
                        },
                        language: {
                            type: "string",
                            enum: ["cpp", "lua"],
                            description: "代码语言",
                            default: "cpp"
                        },
                        analysisType: {
                            type: "string",
                            enum: ["all", "performance", "security", "best_practices"],
                            description: "分析类型",
                            default: "all"
                        }
                    },
                    required: ["code"]
                }
            },
            {
                name: "compare_axmol_versions",
                description: "比较不同 Axmol 版本间的功能差异和兼容性。",
                inputSchema: {
                    type: "object",
                    properties: {
                        feature: {
                            type: "string",
                            description: "要比较的功能或特性"
                        },
                        versions: {
                            type: "array",
                            items: { type: "string" },
                            description: "要比较的版本列表",
                            minItems: 2
                        },
                        includeBreakingChanges: {
                            type: "boolean",
                            description: "是否包含破坏性变更",
                            default: true
                        }
                    },
                    required: ["feature", "versions"]
                }
            },
            {
                name: "get_best_practices",
                description: "获取 Axmol 开发的最佳实践和架构建议。",
                inputSchema: {
                    type: "object",
                    properties: {
                        useCase: {
                            type: "string",
                            description: "使用场景（如 'game_architecture', 'performance', 'memory_management' 等）"
                        },
                        includeExamples: {
                            type: "boolean",
                            description: "是否包含代码示例",
                            default: true
                        },
                        includeAntiPatterns: {
                            type: "boolean",
                            description: "是否包含反模式说明",
                            default: true
                        }
                    },
                    required: ["useCase"]
                }
            },
            {
                name: "search_community_solutions",
                description: "搜索社区解决方案，包括 GitHub Issues、Discussions 和其他社区平台的讨论。",
                inputSchema: {
                    type: "object",
                    properties: {
                        problemDescription: {
                            type: "string",
                            description: "问题描述"
                        },
                        includeDiscussions: {
                            type: "boolean",
                            description: "是否包含社区讨论",
                            default: true
                        },
                        maxResults: {
                            type: "number",
                            description: "最大返回结果数量",
                            default: 10
                        }
                    },
                    required: ["problemDescription"]
                }
            }
        ]
    };
});
// 注册工具调用处理器
server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;
    const startTime = Date.now();
    try {
        console.log(`🔧 调用工具: ${name}`);
        console.log(`📊 当前内存使用: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
        let result;
        switch (name) {
            // 第一阶段：核心工具
            case "search_axmol_documentation":
                if (!args || typeof args.query !== 'string') {
                    throw new Error('缺少必需参数: query');
                }
                result = await documentationService.searchDocumentation(args.query, args.sourceType || 'all', {
                    maxResults: args.maxResults,
                    useCache: args.useCache
                });
                break;
            case "find_code_examples":
                if (!args || typeof args.feature !== 'string') {
                    throw new Error('缺少必需参数: feature');
                }
                result = await codeExampleService.findCodeExamples(args.feature, args.platform || 'all', {
                    language: args.language || 'cpp',
                    maxResults: args.maxResults
                });
                break;
            case "get_api_reference":
                if (!args || typeof args.className !== 'string') {
                    throw new Error('缺少必需参数: className');
                }
                result = await apiReferenceService.getApiReference(args.className, args.methodName);
                break;
            case "solve_build_issue":
                if (!args || typeof args.platform !== 'string' || typeof args.errorMessage !== 'string') {
                    throw new Error('缺少必需参数: platform, errorMessage');
                }
                result = await buildIssueService.solveBuildIssue(args.platform, args.errorMessage);
                break;
            // 第二阶段：增强工具
            case "get_migration_guide":
                if (!args || typeof args.fromEngine !== 'string') {
                    throw new Error('缺少必需参数: fromEngine');
                }
                result = await migrationService.getMigrationGuide(args.fromEngine, args.topic || 'general');
                break;
            case "find_platform_specific_info":
                if (!args || typeof args.platform !== 'string') {
                    throw new Error('缺少必需参数: platform');
                }
                result = await platformService.findPlatformSpecificInfo(args.platform, args.topic || 'build');
                break;
            // 第三阶段：高级工具
            case "analyze_axmol_code":
                if (!args || typeof args.code !== 'string') {
                    throw new Error('缺少必需参数: code');
                }
                result = await codeAnalysisService.analyzeAxmolCode(args.code, args.language || 'cpp');
                break;
            case "compare_axmol_versions":
                if (!args || typeof args.feature !== 'string' || !Array.isArray(args.versions)) {
                    throw new Error('缺少必需参数: feature, versions');
                }
                result = await versionCompareService.compareAxmolVersions(args.feature, args.versions);
                break;
            case "get_best_practices":
                if (!args || typeof args.useCase !== 'string') {
                    throw new Error('缺少必需参数: useCase');
                }
                result = await bestPracticesService.getBestPractices(args.useCase);
                break;
            case "search_community_solutions":
                if (!args || typeof args.problemDescription !== 'string') {
                    throw new Error('缺少必需参数: problemDescription');
                }
                result = await communityService.searchCommunitySolutions(args.problemDescription);
                break;
            default:
                throw new Error(`未知工具: ${name}`);
        }
        const executionTime = Date.now() - startTime;
        console.log(`✅ 工具执行完成: ${name} (${executionTime}ms)`);
        // 统一的响应格式处理
        return formatToolResponse(result, name, executionTime);
    }
    catch (error) {
        const executionTime = Date.now() - startTime;
        const axmolError = errorHandler.handleApiError(error, name, args);
        console.error('❌ MCP工具调用失败:', {
            toolName: name,
            error: axmolError.message,
            executionTime,
            timestamp: new Date().toISOString()
        });
        return {
            content: [
                {
                    type: "text",
                    text: `❌ 工具执行失败: ${axmolError.message}\n\n错误代码: ${axmolError.code}\n执行时间: ${executionTime}ms`,
                },
            ],
        };
    }
});
/**
 * 格式化工具响应
 */
function formatToolResponse(result, toolName, executionTime) {
    if (!result.success) {
        return {
            content: [
                {
                    type: "text",
                    text: `❌ ${toolName} 执行失败: ${result.error?.message || '未知错误'}\n\n错误详情: ${JSON.stringify(result.error, null, 2)}`,
                },
            ],
        };
    }
    const data = result.data;
    const metadata = result.metadata;
    let responseText = '';
    // 根据工具类型格式化响应
    switch (toolName) {
        case 'search_axmol_documentation':
            responseText = formatDocumentationResponse(data, metadata);
            break;
        case 'find_code_examples':
            responseText = formatCodeExamplesResponse(data, metadata);
            break;
        case 'get_api_reference':
            responseText = formatApiReferenceResponse(data, metadata);
            break;
        case 'solve_build_issue':
            responseText = formatBuildIssueResponse(data, metadata);
            break;
        case 'get_migration_guide':
            responseText = formatMigrationGuideResponse(data, metadata);
            break;
        case 'find_platform_specific_info':
            responseText = formatPlatformInfoResponse(data, metadata);
            break;
        case 'analyze_axmol_code':
            responseText = formatCodeAnalysisResponse(data, metadata);
            break;
        case 'compare_axmol_versions':
            responseText = formatVersionComparisonResponse(data, metadata);
            break;
        case 'get_best_practices':
            responseText = formatBestPracticesResponse(data, metadata);
            break;
        case 'search_community_solutions':
            responseText = formatCommunityResponse(data, metadata);
            break;
        default:
            responseText = JSON.stringify(data, null, 2);
    }
    // 添加元数据信息
    responseText += `\n\n---\n📊 **执行信息**\n`;
    responseText += `- 执行时间: ${executionTime}ms\n`;
    responseText += `- 结果数量: ${metadata?.resultsCount || 0}\n`;
    responseText += `- 数据源: ${metadata?.sources?.join(', ') || '未知'}\n`;
    responseText += `- 缓存命中: ${metadata?.cacheHit ? '是' : '否'}\n`;
    return {
        content: [
            {
                type: "text",
                text: responseText,
            },
        ],
    };
}
// 启动服务器
async function main() {
    console.log('🚀 启动 Axmol MCP 服务器...');
    // 显示系统信息
    console.log(`📊 Node.js 版本: ${process.version}`);
    console.log(`💾 内存使用: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
    console.log(`🎯 工作目录: ${process.cwd()}`);
    // 显示数据源状态
    const dataSourceStats = dataSourceManager.getStats();
    console.log(`📡 数据源状态: ${dataSourceStats.healthy}/${dataSourceStats.total} 健康`);
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.log('✅ Axmol MCP 服务器已启动，等待客户端连接...');
    console.log('🔧 可用工具数量: 10');
    console.log('📚 支持的功能:');
    console.log('   - 文档搜索 (search_axmol_documentation)');
    console.log('   - 代码示例查找 (find_code_examples)');
    console.log('   - API参考查询 (get_api_reference)');
    console.log('   - 构建问题诊断 (solve_build_issue)');
    console.log('   - 迁移指南 (get_migration_guide)');
    console.log('   - 平台特定信息 (find_platform_specific_info)');
    console.log('   - 代码分析 (analyze_axmol_code)');
    console.log('   - 版本对比 (compare_axmol_versions)');
    console.log('   - 最佳实践 (get_best_practices)');
    console.log('   - 社区解决方案 (search_community_solutions)');
}
// 启动服务器
main().catch(error => {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map