{"data": [{"useCase": "memory_management", "category": "内存管理", "title": "智能内存管理", "description": "使用 Axmol 的自动内存管理和智能指针", "recommendations": ["优先使用 Axmol 的 create 方法", "正确使用 retain/release 机制", "避免循环引用", "及时释放大型资源", "使用弱引用打破循环依赖"], "examples": [{"title": "正确的内存管理", "language": "cpp", "code": "// 推荐做法\nauto sprite = Sprite::create(\"player.png\");\nthis->addChild(sprite); // 自动管理内存\n\n// 避免的做法\nSprite* sprite = new Sprite();\nsprite->initWithFile(\"player.png\");\n// 容易忘记释放\n\n// 处理循环引用\nclass Player : public Node {\n    WeakPtr<GameScene> scene; // 使用弱引用\n};", "description": "使用 Axmol 的自动内存管理避免内存泄漏"}], "antiPatterns": ["手动 new/delete 对象", "忘记释放纹理和音频资源", "创建循环引用"], "performance": [{"aspect": "内存使用", "recommendation": "及时释放不需要的资源", "impact": "high", "measurement": "内存占用减少 20-50%"}], "resources": [{"type": "official_docs", "title": "Axmol 官方文档", "url": "https://axmol.dev/manual/latest", "content": "Axmol 引擎的官方文档", "relevanceScore": 8, "matchedTerms": ["memory", "内存管理"], "source": "official"}, {"type": "wiki", "title": "Axmol GitHub Wiki", "url": "https://github.com/axmolengine/axmol/wiki", "content": "Axmol 引擎的 GitHub Wiki", "relevanceScore": 7, "matchedTerms": ["memory"], "source": "community"}]}], "timestamp": 1750515042208, "ttl": 7200000, "key": "best_practices_memory_{}"}