/**
 * 响应格式化工具
 * 负责将各种服务的响应格式化为用户友好的文本
 */
import { AxmolResource, CodeExample, ApiReference, BuildIssue, MigrationGuide, PlatformInfo, CodeAnalysisResult, VersionComparison, BestPractice, CommunitySolution } from '../types/index.js';
/**
 * 格式化文档搜索响应
 */
export declare function formatDocumentationResponse(data: AxmolResource[], metadata: any): string;
/**
 * 格式化代码示例响应
 */
export declare function formatCodeExamplesResponse(data: CodeExample[], metadata: any): string;
/**
 * 格式化API参考响应
 */
export declare function formatApiReferenceResponse(data: ApiReference, metadata: any): string;
/**
 * 格式化构建问题响应
 */
export declare function formatBuildIssueResponse(data: BuildIssue, metadata: any): string;
/**
 * 格式化迁移指南响应
 */
export declare function formatMigrationGuideResponse(data: MigrationGuide, metadata: any): string;
/**
 * 格式化平台信息响应
 */
export declare function formatPlatformInfoResponse(data: PlatformInfo, metadata: any): string;
/**
 * 格式化代码分析响应
 */
export declare function formatCodeAnalysisResponse(data: CodeAnalysisResult, metadata: any): string;
/**
 * 格式化版本对比响应
 */
export declare function formatVersionComparisonResponse(data: VersionComparison, metadata: any): string;
/**
 * 格式化最佳实践响应
 */
export declare function formatBestPracticesResponse(data: BestPractice[], metadata: any): string;
/**
 * 格式化社区解决方案响应
 */
export declare function formatCommunityResponse(data: CommunitySolution, metadata: any): string;
//# sourceMappingURL=responseFormatter.d.ts.map