/**
 * API参考服务
 * 负责获取和提供 Axmol API 参考文档
 */
import * as cheerio from 'cheerio';
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
export class ApiReferenceService {
    OFFICIAL_DOCS_BASE = 'https://axmol.dev/manual/latest';
    GITHUB_RAW_BASE = 'https://raw.githubusercontent.com';
    AXMOL_REPO = 'axmolengine/axmol';
    CACHE_TTL = 2 * 60 * 60 * 1000; // 2小时缓存
    /**
     * 获取API参考文档
     */
    async getApiReference(className, methodName, options = {}) {
        const startTime = Date.now();
        try {
            console.log(`🔍 获取API参考: ${className}${methodName ? `::${methodName}` : ''}`);
            // 生成缓存键
            const cacheKey = `api_ref_${className}_${methodName || 'all'}_${JSON.stringify(options)}`;
            // 尝试从缓存获取
            if (options.useCache !== false) {
                const cached = await defaultCache.get(cacheKey);
                if (cached) {
                    console.log('✅ 从缓存获取API参考');
                    return {
                        success: true,
                        data: cached,
                        metadata: {
                            searchTime: Date.now() - startTime,
                            resultsCount: 1,
                            sources: ['cache'],
                            cacheHit: true
                        }
                    };
                }
            }
            const sources = [];
            let apiReference = null;
            // 首先尝试从官方文档获取
            apiReference = await this.getOfficialApiReference(className, methodName);
            if (apiReference) {
                sources.push('official_docs');
            }
            // 如果官方文档没有找到，尝试从源码头文件获取
            if (!apiReference) {
                apiReference = await this.getSourceCodeApiReference(className, methodName);
                if (apiReference) {
                    sources.push('source_code');
                }
            }
            // 如果还是没有找到，尝试生成基础的API信息
            if (!apiReference) {
                apiReference = await this.generateBasicApiReference(className, methodName);
                if (apiReference) {
                    sources.push('generated');
                }
            }
            if (!apiReference) {
                throw new Error(`未找到 ${className} 的API参考文档`);
            }
            // 缓存结果
            if (options.useCache !== false) {
                await defaultCache.set(cacheKey, apiReference, this.CACHE_TTL);
            }
            console.log(`✅ API参考获取完成: ${className}`);
            return {
                success: true,
                data: apiReference,
                metadata: {
                    searchTime: Date.now() - startTime,
                    resultsCount: 1,
                    sources,
                    cacheHit: false
                }
            };
        }
        catch (error) {
            const axmolError = errorHandler.handleApiError(error, 'getApiReference', { className, methodName, options });
            return {
                success: false,
                error: axmolError,
                metadata: {
                    searchTime: Date.now() - startTime,
                    resultsCount: 0,
                    sources: [],
                    cacheHit: false
                }
            };
        }
    }
    /**
     * 从官方文档获取API参考
     */
    async getOfficialApiReference(className, methodName) {
        try {
            // 构建可能的API页面URL
            const possibleUrls = this.generateApiUrls(className);
            for (const url of possibleUrls) {
                try {
                    const response = await networkUtils.get(url, {
                        timeout: 10000,
                        validateStatus: (status) => status === 200
                    });
                    const $ = cheerio.load(response.data);
                    const apiRef = this.parseOfficialApiPage($, className, methodName, url);
                    if (apiRef) {
                        console.log(`✅ 从官方文档获取API: ${className}`);
                        return apiRef;
                    }
                }
                catch (error) {
                    // 继续尝试下一个URL
                    continue;
                }
            }
        }
        catch (error) {
            console.log('⚠️ 官方API文档获取失败:', error instanceof Error ? error.message : String(error));
        }
        return null;
    }
    /**
     * 生成可能的API页面URL
     */
    generateApiUrls(className) {
        const urls = [];
        const classNameLower = className.toLowerCase();
        // 常见的类名到URL的映射模式
        const urlPatterns = [
            `classax_1_1_${classNameLower}.html`,
            `classax_1_1${classNameLower}.html`,
            `class_${classNameLower}.html`,
            `${classNameLower}.html`
        ];
        urlPatterns.forEach(pattern => {
            urls.push(`${this.OFFICIAL_DOCS_BASE}/${pattern}`);
        });
        return urls;
    }
    /**
     * 解析官方API页面
     */
    parseOfficialApiPage($, className, methodName, url) {
        try {
            const title = $('title').text() || $('h1').first().text();
            if (!title.toLowerCase().includes(className.toLowerCase())) {
                return null;
            }
            // 提取类描述
            const description = $('.brief, .description, .detailed-description').first().text().trim() ||
                $('p').first().text().trim() ||
                '暂无描述';
            // 提取方法信息
            const methods = this.extractMethods($, methodName);
            // 提取相关API
            const relatedApis = this.extractRelatedApis($);
            // 检测命名空间
            const namespace = this.detectNamespace($, className);
            return {
                className,
                methodName,
                namespace,
                description,
                parameters: methods.length > 0 ? methods[0].parameters : [],
                returnType: methods.length > 0 ? methods[0].returnType : undefined,
                examples: this.extractExamples($),
                relatedApis,
                sourceFile: this.detectSourceFile($),
                documentationUrl: url
            };
        }
        catch (error) {
            console.log('⚠️ 解析API页面失败:', error instanceof Error ? error.message : String(error));
            return null;
        }
    }
    /**
     * 提取方法信息
     */
    extractMethods($, targetMethod) {
        const methods = [];
        // 查找方法定义
        $('.method, .function, .member').each((_, element) => {
            const $element = $(element);
            const methodText = $element.text();
            if (targetMethod && !methodText.toLowerCase().includes(targetMethod.toLowerCase())) {
                return;
            }
            const methodInfo = this.parseMethodSignature(methodText);
            if (methodInfo) {
                methods.push(methodInfo);
            }
        });
        return methods;
    }
    /**
     * 解析方法签名
     */
    parseMethodSignature(signature) {
        try {
            // 简化的方法签名解析
            const match = signature.match(/(\w+)\s+(\w+)\s*\(([^)]*)\)/);
            if (!match)
                return null;
            const [, returnType, name, paramStr] = match;
            const parameters = this.parseParameters(paramStr);
            return {
                name,
                parameters,
                returnType,
                description: '从API文档提取的方法'
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * 解析参数
     */
    parseParameters(paramStr) {
        if (!paramStr.trim())
            return [];
        const parameters = [];
        const params = paramStr.split(',');
        params.forEach(param => {
            const trimmed = param.trim();
            if (trimmed) {
                const parts = trimmed.split(/\s+/);
                if (parts.length >= 2) {
                    parameters.push({
                        name: parts[parts.length - 1],
                        type: parts.slice(0, -1).join(' '),
                        description: '参数描述',
                        optional: trimmed.includes('=') || trimmed.includes('default')
                    });
                }
            }
        });
        return parameters;
    }
    /**
     * 提取相关API
     */
    extractRelatedApis($) {
        const relatedApis = [];
        $('.related, .see-also, .inheritance').find('a').each((_, element) => {
            const text = $(element).text().trim();
            if (text && text.length > 0) {
                relatedApis.push(text);
            }
        });
        return [...new Set(relatedApis)].slice(0, 10);
    }
    /**
     * 提取示例代码
     */
    extractExamples($) {
        const examples = [];
        $('pre, code').each((_, element) => {
            const code = $(element).text().trim();
            if (code.length > 50) { // 过滤掉太短的代码片段
                examples.push({
                    title: 'API使用示例',
                    language: 'cpp',
                    code,
                    description: '从API文档提取的示例代码'
                });
            }
        });
        return examples.slice(0, 3);
    }
    /**
     * 检测命名空间
     */
    detectNamespace($, className) {
        const text = $('body').text();
        // 查找命名空间声明
        const namespaceMatch = text.match(/namespace\s+(\w+)/i);
        if (namespaceMatch) {
            return namespaceMatch[1];
        }
        // 默认为ax命名空间
        return 'ax';
    }
    /**
     * 检测源文件
     */
    detectSourceFile($) {
        // 查找源文件信息
        const sourceInfo = $('.source-file, .file-info').text();
        if (sourceInfo) {
            const match = sourceInfo.match(/(\w+\.(h|hpp|cpp))/);
            if (match) {
                return match[1];
            }
        }
        return '未知';
    }
    /**
     * 从源码获取API参考
     */
    async getSourceCodeApiReference(className, methodName) {
        try {
            // 查找可能的头文件路径
            const possiblePaths = this.generateHeaderPaths(className);
            for (const path of possiblePaths) {
                try {
                    const fileUrl = `${this.GITHUB_RAW_BASE}/${this.AXMOL_REPO}/dev/${path}`;
                    const response = await networkUtils.get(fileUrl, { timeout: 8000 });
                    const apiRef = this.parseSourceCodeApi(response.data, className, methodName, path);
                    if (apiRef) {
                        console.log(`✅ 从源码获取API: ${className}`);
                        return apiRef;
                    }
                }
                catch (error) {
                    continue;
                }
            }
        }
        catch (error) {
            console.log('⚠️ 源码API获取失败:', error instanceof Error ? error.message : String(error));
        }
        return null;
    }
    /**
     * 生成可能的头文件路径
     */
    generateHeaderPaths(className) {
        const paths = [];
        const classNameVariations = [className, className.toLowerCase()];
        const basePaths = [
            'core/2d',
            'core/3d',
            'core/base',
            'core/ui',
            'core/audio',
            'core/physics',
            'core/renderer'
        ];
        basePaths.forEach(basePath => {
            classNameVariations.forEach(name => {
                paths.push(`${basePath}/${name}.h`);
                paths.push(`${basePath}/${name}.hpp`);
            });
        });
        return paths;
    }
    /**
     * 解析源码API
     */
    parseSourceCodeApi(content, className, methodName, filePath) {
        try {
            // 查找类定义
            const classMatch = content.match(new RegExp(`class\\s+${className}[^{]*{([^}]*)}`, 'i'));
            if (!classMatch)
                return null;
            const classContent = classMatch[1];
            // 提取方法
            const methods = this.extractMethodsFromSource(classContent, methodName);
            return {
                className,
                methodName,
                namespace: 'ax',
                description: `${className} 类的API参考（从源码提取）`,
                parameters: methods.length > 0 ? methods[0].parameters : [],
                returnType: methods.length > 0 ? methods[0].returnType : undefined,
                examples: [],
                relatedApis: [],
                sourceFile: filePath,
                documentationUrl: `https://github.com/${this.AXMOL_REPO}/blob/dev/${filePath}`
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * 从源码提取方法
     */
    extractMethodsFromSource(content, targetMethod) {
        const methods = [];
        // 简化的方法提取逻辑
        const methodRegex = /(\w+)\s+(\w+)\s*\(([^)]*)\)\s*[;{]/g;
        let match;
        while ((match = methodRegex.exec(content)) !== null) {
            const [, returnType, name, paramStr] = match;
            if (targetMethod && !name.toLowerCase().includes(targetMethod.toLowerCase())) {
                continue;
            }
            methods.push({
                name,
                parameters: this.parseParameters(paramStr),
                returnType,
                description: `${name} 方法（从源码提取）`
            });
        }
        return methods;
    }
    /**
     * 生成基础API参考
     */
    async generateBasicApiReference(className, methodName) {
        // 基于类名生成基础的API信息
        return {
            className,
            methodName,
            namespace: 'ax',
            description: `${className} 是 Axmol 引擎中的一个类。具体的API文档可能需要查看官方文档或源码。`,
            parameters: [],
            examples: [],
            relatedApis: [],
            sourceFile: '未知',
            documentationUrl: this.OFFICIAL_DOCS_BASE
        };
    }
}
// 导出默认API参考服务实例
export const apiReferenceService = new ApiReferenceService();
//# sourceMappingURL=apiReferenceService.js.map