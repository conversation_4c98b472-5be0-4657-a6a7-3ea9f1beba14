#!/usr/bin/env node

/**
 * 全面测试所有 10 个 MCP 工具的功能
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 全面测试所有 Axmol MCP 工具功能...\n');

// 详细的测试用例
const comprehensiveTests = [
  {
    name: "search_axmol_documentation",
    description: "搜索 Axmol 官方文档",
    request: {
      query: "sprite",
      sourceType: "all",
      maxResults: 3,
      useCache: false
    },
    expectedInResponse: ["sprite", "文档", "axmol"],
    timeout: 15000
  },
  {
    name: "find_code_examples", 
    description: "查找代码示例",
    request: {
      feature: "sprite",
      platform: "all",
      language: "cpp",
      maxResults: 3
    },
    expectedInResponse: ["sprite", "代码", "示例"],
    timeout: 12000
  },
  {
    name: "get_api_reference",
    description: "获取 API 参考",
    request: {
      className: "Sprite",
      methodName: "create"
    },
    expectedInResponse: ["Sprite", "API", "参考"],
    timeout: 10000
  },
  {
    name: "solve_build_issue",
    description: "解决构建问题", 
    request: {
      platform: "windows",
      errorMessage: "CMake configuration failed",
      buildTool: "cmake"
    },
    expectedInResponse: ["构建", "CMake", "解决"],
    timeout: 10000
  },
  {
    name: "get_migration_guide",
    description: "获取迁移指南",
    request: {
      fromEngine: "cocos2d-x-3.x",
      topic: "sprite",
      includeExamples: true
    },
    expectedInResponse: ["迁移", "cocos2d-x", "axmol"],
    timeout: 10000
  },
  {
    name: "find_platform_specific_info",
    description: "查找平台特定信息",
    request: {
      platform: "android",
      topic: "build",
      includeExamples: true
    },
    expectedInResponse: ["android", "平台", "构建"],
    timeout: 10000
  },
  {
    name: "analyze_axmol_code",
    description: "分析 Axmol 代码",
    request: {
      code: "auto sprite = Sprite::create(\"test.png\"); sprite->setPosition(100, 200);",
      language: "cpp",
      analysisType: "full"
    },
    expectedInResponse: ["代码", "分析", "Sprite"],
    timeout: 10000
  },
  {
    name: "compare_axmol_versions",
    description: "比较 Axmol 版本",
    request: {
      feature: "sprite",
      versions: ["2.0.0", "2.1.0"],
      includeBreakingChanges: true
    },
    expectedInResponse: ["版本", "对比", "sprite"],
    timeout: 10000
  },
  {
    name: "get_best_practices",
    description: "获取最佳实践",
    request: {
      useCase: "performance",
      includeExamples: true,
      includeAntiPatterns: true
    },
    expectedInResponse: ["最佳", "实践", "性能"],
    timeout: 10000
  },
  {
    name: "search_community_solutions",
    description: "搜索社区解决方案",
    request: {
      problemDescription: "sprite rendering performance issue",
      includeDiscussions: true,
      maxResults: 5
    },
    expectedInResponse: ["社区", "解决", "方案"],
    timeout: 12000
  }
];

// 测试单个工具的详细功能
function testToolComprehensively(testCase) {
  return new Promise((resolve) => {
    console.log(`\n${'='.repeat(70)}`);
    console.log(`🔧 测试工具: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`📋 请求参数:`);
    console.log(JSON.stringify(testCase.request, null, 2));
    console.log(`${'='.repeat(70)}`);
    
    const serverPath = path.join(__dirname, 'dist', 'index.js');
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname
    });

    let output = '';
    let errorOutput = '';
    let responseReceived = false;
    let responseContent = '';

    server.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      // 检查是否收到完整响应
      if (text.includes('"content"') && !responseReceived) {
        responseReceived = true;
        responseContent = text;
        console.log('   ✅ 收到工具响应');
        
        // 延迟关闭以确保完整输出
        setTimeout(() => {
          server.kill();
        }, 1000);
      }
    });

    server.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // 等待服务器启动后发送请求
    setTimeout(() => {
      const mcpRequest = {
        jsonrpc: "2.0",
        id: 1,
        method: "tools/call",
        params: {
          name: testCase.name,
          arguments: testCase.request
        }
      };
      
      try {
        console.log('   📤 发送 MCP 请求...');
        server.stdin.write(JSON.stringify(mcpRequest) + '\n');
      } catch (error) {
        console.log(`   ❌ 发送请求失败: ${error.message}`);
        server.kill();
      }
    }, 3000);

    // 设置超时
    const timeout = setTimeout(() => {
      if (!responseReceived) {
        console.log(`   ❌ 工具响应超时 (${testCase.timeout}ms)`);
        server.kill();
      }
    }, testCase.timeout);

    server.on('close', (code) => {
      clearTimeout(timeout);
      
      // 分析响应内容
      const result = analyzeToolResponse(testCase, responseContent, output, errorOutput, responseReceived);
      resolve(result);
    });

    server.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`   ❌ 服务器错误: ${error.message}`);
      resolve({
        toolName: testCase.name,
        success: false,
        error: error.message,
        details: { hasResponse: false, contentCheck: false, errorCount: 1 }
      });
    });
  });
}

// 分析工具响应
function analyzeToolResponse(testCase, responseContent, fullOutput, errorOutput, hasResponse) {
  console.log('\n   📊 分析响应结果:');
  
  const result = {
    toolName: testCase.name,
    success: false,
    details: {
      hasResponse,
      contentCheck: false,
      keywordMatches: 0,
      errorCount: 0,
      responseLength: responseContent.length
    }
  };

  // 1. 检查是否有响应
  if (!hasResponse) {
    console.log('   ❌ 未收到工具响应');
    return result;
  }
  console.log('   ✅ 工具响应正常');

  // 2. 检查响应内容长度
  if (responseContent.length < 50) {
    console.log('   ⚠️ 响应内容过短');
  } else {
    console.log(`   ✅ 响应内容充足 (${responseContent.length} 字符)`);
    result.details.contentCheck = true;
  }

  // 3. 检查关键词匹配
  const responseText = responseContent.toLowerCase();
  let keywordMatches = 0;
  
  console.log('   🔍 关键词匹配检查:');
  testCase.expectedInResponse.forEach(keyword => {
    const found = responseText.includes(keyword.toLowerCase());
    console.log(`      ${found ? '✅' : '❌'} "${keyword}"`);
    if (found) keywordMatches++;
  });
  
  result.details.keywordMatches = keywordMatches;
  const keywordScore = keywordMatches / testCase.expectedInResponse.length;
  console.log(`   📈 关键词匹配率: ${keywordMatches}/${testCase.expectedInResponse.length} (${(keywordScore * 100).toFixed(1)}%)`);

  // 4. 检查错误输出
  const criticalErrors = errorOutput.split('\n').filter(line => 
    line.includes('Error') && 
    !line.includes('持久化缓存项失败') && 
    !line.includes('writeJson is not a function')
  );
  
  result.details.errorCount = criticalErrors.length;
  if (criticalErrors.length > 0) {
    console.log(`   ⚠️ 发现 ${criticalErrors.length} 个关键错误`);
    criticalErrors.slice(0, 2).forEach(error => {
      console.log(`      - ${error.substring(0, 100)}...`);
    });
  } else {
    console.log('   ✅ 无关键错误');
  }

  // 5. 综合评估
  const hasGoodResponse = result.details.contentCheck;
  const hasGoodKeywords = keywordScore >= 0.5; // 至少50%关键词匹配
  const hasMinimalErrors = result.details.errorCount <= 1;

  result.success = hasResponse && hasGoodResponse && hasGoodKeywords && hasMinimalErrors;

  console.log(`\n   🎯 工具测试结果: ${result.success ? '✅ 通过' : '❌ 失败'}`);
  
  return result;
}

async function main() {
  console.log(`🎯 准备全面测试 ${comprehensiveTests.length} 个 MCP 工具\n`);
  
  const results = [];
  let totalTests = comprehensiveTests.length;
  let passedTests = 0;
  
  for (let i = 0; i < comprehensiveTests.length; i++) {
    const testCase = comprehensiveTests[i];
    
    console.log(`\n📋 进度: ${i + 1}/${totalTests}`);
    
    try {
      const result = await testToolComprehensively(testCase);
      results.push(result);
      
      if (result.success) {
        passedTests++;
        console.log(`🏁 ${testCase.name} - ✅ 测试通过`);
      } else {
        console.log(`🏁 ${testCase.name} - ❌ 测试失败`);
      }
      
    } catch (error) {
      console.log(`❌ ${testCase.name} 测试异常: ${error.message}`);
      results.push({
        toolName: testCase.name,
        success: false,
        error: error.message,
        details: { hasResponse: false, contentCheck: false, errorCount: 1 }
      });
    }
    
    // 测试间隔，避免资源冲突
    if (i < comprehensiveTests.length - 1) {
      console.log('   ⏳ 等待 2 秒后继续下一个测试...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // 生成详细测试报告
  generateDetailedReport(results, passedTests, totalTests);
}

function generateDetailedReport(results, passedTests, totalTests) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📊 详细测试报告 - Axmol MCP 工具集`);
  console.log(`${'='.repeat(80)}`);
  
  console.log(`\n🎯 总体统计:`);
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   通过测试: ${passedTests}`);
  console.log(`   失败测试: ${totalTests - passedTests}`);
  console.log(`   成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);
  
  console.log(`\n📋 详细结果:`);
  results.forEach((result, index) => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    const testCase = comprehensiveTests[index];
    console.log(`\n${(index + 1).toString().padStart(2)}. ${status} ${result.toolName}`);
    console.log(`    描述: ${testCase.description}`);
    
    if (result.details) {
      console.log(`    响应: ${result.details.hasResponse ? '✅' : '❌'}`);
      console.log(`    内容: ${result.details.contentCheck ? '✅' : '❌'}`);
      console.log(`    关键词: ${result.details.keywordMatches}/${testCase.expectedInResponse.length}`);
      console.log(`    错误数: ${result.details.errorCount}`);
    }
    
    if (result.error) {
      console.log(`    错误: ${result.error}`);
    }
  });
  
  // 失败分析
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log(`\n⚠️ 失败测试分析:`);
    failedTests.forEach(failed => {
      console.log(`   - ${failed.toolName}: ${failed.error || '功能测试未通过'}`);
    });
  }
  
  // 最终结论
  console.log(`\n${'='.repeat(80)}`);
  if (passedTests === totalTests) {
    console.log(`🎉 所有 MCP 工具测试全部通过！`);
    console.log(`🚀 Axmol MCP 工具集功能完整，可以投入生产使用！`);
  } else if (passedTests >= totalTests * 0.8) {
    console.log(`✅ 大部分工具测试通过 (${passedTests}/${totalTests})`);
    console.log(`💡 建议检查失败的工具并进行优化`);
  } else {
    console.log(`⚠️ 多个工具测试未通过 (${totalTests - passedTests}/${totalTests})`);
    console.log(`🔧 需要进一步调试和修复`);
  }
  console.log(`${'='.repeat(80)}`);
}

main().catch(error => {
  console.error('❌ 全面测试失败:', error);
  process.exit(1);
});
