#!/usr/bin/env node

/**
 * 测试优化后的工具
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 测试优化后的 Axmol MCP 工具...\n');

// 重点测试之前失败的工具
const optimizedTests = [
  {
    name: "search_axmol_documentation",
    description: "搜索 Axmol 官方文档 (优化后)",
    request: {
      query: "sprite",
      sourceType: "official",
      maxResults: 2,
      useCache: false
    },
    expectedInResponse: ["sprite", "文档", "axmol"],
    timeout: 20000
  },
  {
    name: "find_code_examples", 
    description: "查找代码示例 (优化后)",
    request: {
      feature: "sprite",
      platform: "all",
      language: "cpp",
      maxResults: 2
    },
    expectedInResponse: ["sprite", "代码", "示例"],
    timeout: 15000
  },
  {
    name: "get_migration_guide",
    description: "获取迁移指南 (优化后)",
    request: {
      fromEngine: "cocos2d-x-3.x",
      topic: "general"
    },
    expectedInResponse: ["迁移", "cocos2d-x", "axmol"],
    timeout: 12000
  }
];

// 测试单个工具
function testOptimizedTool(testCase) {
  return new Promise((resolve) => {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🔧 测试工具: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`⏱️ 超时设置: ${testCase.timeout}ms`);
    console.log(`${'='.repeat(60)}`);
    
    const serverPath = path.join(__dirname, 'dist', 'index.js');
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe'],
      cwd: __dirname
    });

    let output = '';
    let errorOutput = '';
    let responseReceived = false;
    let responseContent = '';
    let startTime = Date.now();

    server.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      if (text.includes('"content"') && !responseReceived) {
        responseReceived = true;
        responseContent = text;
        const responseTime = Date.now() - startTime;
        console.log(`   ✅ 收到响应 (${responseTime}ms)`);
        
        setTimeout(() => {
          server.kill();
        }, 1000);
      }
    });

    server.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // 发送请求
    setTimeout(() => {
      const mcpRequest = {
        jsonrpc: "2.0",
        id: 1,
        method: "tools/call",
        params: {
          name: testCase.name,
          arguments: testCase.request
        }
      };
      
      console.log('   📤 发送请求...');
      startTime = Date.now();
      
      try {
        server.stdin.write(JSON.stringify(mcpRequest) + '\n');
      } catch (error) {
        console.log(`   ❌ 发送请求失败: ${error.message}`);
        server.kill();
      }
    }, 3000);

    // 超时处理
    const timeout = setTimeout(() => {
      if (!responseReceived) {
        const elapsedTime = Date.now() - startTime;
        console.log(`   ❌ 工具响应超时 (${elapsedTime}ms)`);
        server.kill();
      }
    }, testCase.timeout);

    server.on('close', (code) => {
      clearTimeout(timeout);
      
      const result = analyzeOptimizedResponse(testCase, responseContent, output, errorOutput, responseReceived);
      resolve(result);
    });

    server.on('error', (error) => {
      clearTimeout(timeout);
      console.log(`   ❌ 服务器错误: ${error.message}`);
      resolve({
        toolName: testCase.name,
        success: false,
        error: error.message,
        details: { hasResponse: false, contentCheck: false, errorCount: 1 }
      });
    });
  });
}

// 分析优化后的响应
function analyzeOptimizedResponse(testCase, responseContent, fullOutput, errorOutput, hasResponse) {
  console.log('\n   📊 分析优化后的响应:');
  
  const result = {
    toolName: testCase.name,
    success: false,
    details: {
      hasResponse,
      contentCheck: false,
      keywordMatches: 0,
      errorCount: 0,
      responseLength: responseContent.length,
      optimizationEffective: false
    }
  };

  // 1. 检查响应状态
  if (!hasResponse) {
    console.log('   ❌ 仍然未收到响应');
    return result;
  }
  console.log('   ✅ 优化后响应正常');

  // 2. 检查响应内容
  if (responseContent.length < 50) {
    console.log('   ⚠️ 响应内容仍然过短');
  } else {
    console.log(`   ✅ 响应内容充足 (${responseContent.length} 字符)`);
    result.details.contentCheck = true;
  }

  // 3. 关键词匹配检查
  const responseText = responseContent.toLowerCase();
  let keywordMatches = 0;
  
  console.log('   🔍 关键词匹配检查:');
  testCase.expectedInResponse.forEach(keyword => {
    const found = responseText.includes(keyword.toLowerCase());
    console.log(`      ${found ? '✅' : '❌'} "${keyword}"`);
    if (found) keywordMatches++;
  });
  
  result.details.keywordMatches = keywordMatches;
  const keywordScore = keywordMatches / testCase.expectedInResponse.length;
  console.log(`   📈 关键词匹配率: ${keywordMatches}/${testCase.expectedInResponse.length} (${(keywordScore * 100).toFixed(1)}%)`);

  // 4. 检查优化效果
  const hasTimeoutErrors = errorOutput.includes('timeout') || errorOutput.includes('超时');
  const hasNetworkErrors = errorOutput.includes('ECONNRESET') || errorOutput.includes('ETIMEDOUT');
  
  if (!hasTimeoutErrors && !hasNetworkErrors) {
    console.log('   ✅ 优化有效：无超时或网络错误');
    result.details.optimizationEffective = true;
  } else {
    console.log('   ⚠️ 仍有网络相关问题');
  }

  // 5. 综合评估
  const hasGoodResponse = result.details.contentCheck;
  const hasGoodKeywords = keywordScore >= 0.5;
  const isOptimized = result.details.optimizationEffective;

  result.success = hasResponse && hasGoodResponse && hasGoodKeywords && isOptimized;

  console.log(`\n   🎯 优化后测试结果: ${result.success ? '✅ 通过' : '❌ 仍需改进'}`);
  
  return result;
}

async function main() {
  console.log(`🎯 测试 ${optimizedTests.length} 个优化后的工具\n`);
  
  const results = [];
  let passedTests = 0;
  
  for (let i = 0; i < optimizedTests.length; i++) {
    const testCase = optimizedTests[i];
    
    console.log(`📋 进度: ${i + 1}/${optimizedTests.length}`);
    
    try {
      const result = await testOptimizedTool(testCase);
      results.push(result);
      
      if (result.success) {
        passedTests++;
        console.log(`🏁 ${testCase.name} - ✅ 优化成功`);
      } else {
        console.log(`🏁 ${testCase.name} - ⚠️ 仍需改进`);
      }
      
    } catch (error) {
      console.log(`❌ ${testCase.name} 测试异常: ${error.message}`);
      results.push({
        toolName: testCase.name,
        success: false,
        error: error.message,
        details: { hasResponse: false, contentCheck: false, errorCount: 1 }
      });
    }
    
    // 测试间隔
    if (i < optimizedTests.length - 1) {
      console.log('   ⏳ 等待 3 秒后继续...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // 生成优化报告
  generateOptimizationReport(results, passedTests, optimizedTests.length);
}

function generateOptimizationReport(results, passedTests, totalTests) {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`📊 优化效果报告`);
  console.log(`${'='.repeat(80)}`);
  
  console.log(`\n🎯 优化前后对比:`);
  console.log(`   之前失败的工具: 3/3 (100% 失败)`);
  console.log(`   优化后通过: ${passedTests}/${totalTests} (${(passedTests / totalTests * 100).toFixed(1)}% 成功)`);
  console.log(`   改进效果: ${passedTests > 0 ? '✅ 显著改善' : '❌ 仍需努力'}`);
  
  console.log(`\n📋 详细结果:`);
  results.forEach((result, index) => {
    const status = result.success ? '✅ 通过' : '❌ 失败';
    const testCase = optimizedTests[index];
    console.log(`\n${(index + 1).toString().padStart(2)}. ${status} ${result.toolName}`);
    console.log(`    描述: ${testCase.description}`);
    
    if (result.details) {
      console.log(`    响应: ${result.details.hasResponse ? '✅' : '❌'}`);
      console.log(`    内容: ${result.details.contentCheck ? '✅' : '❌'}`);
      console.log(`    关键词: ${result.details.keywordMatches}/${testCase.expectedInResponse.length}`);
      console.log(`    优化效果: ${result.details.optimizationEffective ? '✅' : '❌'}`);
    }
  });
  
  console.log(`\n${'='.repeat(80)}`);
  if (passedTests === totalTests) {
    console.log(`🎉 所有优化工具测试通过！优化非常成功！`);
  } else if (passedTests > 0) {
    console.log(`✅ 部分工具优化成功 (${passedTests}/${totalTests})`);
    console.log(`💡 建议继续优化剩余工具的网络请求策略`);
  } else {
    console.log(`⚠️ 优化效果有限，需要进一步调整策略`);
  }
  console.log(`${'='.repeat(80)}`);
}

main().catch(error => {
  console.error('❌ 优化测试失败:', error);
  process.exit(1);
});
