/**
 * 代码示例服务
 * 负责查找和提供 Axmol 相关的代码示例
 */
import { networkUtils } from '../utils/networkUtils.js';
import { defaultCache } from '../utils/cacheUtils.js';
import { errorHandler } from '../utils/errorHandler.js';
export class CodeExampleService {
    GITHUB_API_BASE = 'https://api.github.com';
    GITHUB_RAW_BASE = 'https://raw.githubusercontent.com';
    AXMOL_REPO = 'axmolengine/axmol';
    CACHE_TTL = 60 * 60 * 1000; // 1小时缓存
    /**
     * 查找代码示例
     */
    async findCodeExamples(feature, platform = 'all', options = {}) {
        const startTime = Date.now();
        try {
            console.log(`🔍 开始搜索代码示例: "${feature}" (平台: ${platform})`);
            // 生成缓存键
            const cacheKey = `code_examples_${feature}_${platform}_${JSON.stringify(options)}`;
            // 尝试从缓存获取
            if (options.useCache !== false) {
                const cached = await defaultCache.get(cacheKey);
                if (cached) {
                    console.log('✅ 从缓存获取代码示例');
                    return {
                        success: true,
                        data: cached,
                        metadata: {
                            searchTime: Date.now() - startTime,
                            resultsCount: cached.length,
                            sources: ['cache'],
                            cacheHit: true
                        }
                    };
                }
            }
            const results = [];
            const sources = [];
            // 搜索官方示例
            const officialExamples = await this.searchOfficialExamples(feature, platform, options);
            results.push(...officialExamples);
            if (officialExamples.length > 0)
                sources.push('official_examples');
            // 搜索测试代码
            const testExamples = await this.searchTestCode(feature, platform, options);
            results.push(...testExamples);
            if (testExamples.length > 0)
                sources.push('test_code');
            // 搜索源码示例
            const sourceExamples = await this.searchSourceCodeExamples(feature, platform, options);
            results.push(...sourceExamples);
            if (sourceExamples.length > 0)
                sources.push('source_code');
            // 按相关性和质量排序
            const sortedResults = this.sortExamplesByQuality(results, feature);
            // 限制结果数量
            const maxResults = options.maxResults || 15;
            const finalResults = sortedResults.slice(0, maxResults);
            // 缓存结果
            if (options.useCache !== false && finalResults.length > 0) {
                await defaultCache.set(cacheKey, finalResults, this.CACHE_TTL);
            }
            console.log(`✅ 代码示例搜索完成: 找到 ${finalResults.length} 个示例`);
            return {
                success: true,
                data: finalResults,
                metadata: {
                    searchTime: Date.now() - startTime,
                    resultsCount: finalResults.length,
                    sources,
                    cacheHit: false
                }
            };
        }
        catch (error) {
            const axmolError = errorHandler.handleApiError(error, 'findCodeExamples', { feature, platform, options });
            return {
                success: false,
                error: axmolError,
                metadata: {
                    searchTime: Date.now() - startTime,
                    resultsCount: 0,
                    sources: [],
                    cacheHit: false
                }
            };
        }
    }
    /**
     * 搜索官方示例代码
     */
    async searchOfficialExamples(feature, platform, options) {
        const results = [];
        // 减少搜索路径，优先搜索最相关的
        const examplePaths = ['tests/cpp-tests'];
        if (options.language === 'lua') {
            examplePaths.push('tests/lua-tests');
        }
        try {
            // 只搜索第一个路径，避免超时
            for (const path of examplePaths.slice(0, 1)) {
                try {
                    const examples = await Promise.race([
                        this.searchExamplesInPath(path, feature, platform, options),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Search timeout')), 8000))
                    ]);
                    results.push(...examples);
                    break; // 找到结果就停止
                }
                catch (error) {
                    console.log(`⚠️ 路径 ${path} 搜索超时或失败`);
                    continue;
                }
            }
            console.log(`📚 官方示例搜索完成: ${results.length} 个示例`);
        }
        catch (error) {
            console.log('⚠️ 官方示例搜索失败:', error instanceof Error ? error.message : String(error));
        }
        return results;
    }
    /**
     * 在指定路径中搜索示例
     */
    async searchExamplesInPath(path, feature, platform, options) {
        const results = [];
        try {
            const contentsUrl = `${this.GITHUB_API_BASE}/repos/${this.AXMOL_REPO}/contents/${path}`;
            const response = await networkUtils.get(contentsUrl, {
                headers: networkUtils.getGitHubHeaders(),
                timeout: 10000
            });
            const files = response.data.filter((item) => item.type === 'file' &&
                this.isRelevantFile(item.name, feature, options.language));
            // 进一步限制文件数量并添加超时控制
            for (const file of files.slice(0, 3)) {
                try {
                    const example = await Promise.race([
                        this.extractCodeExample(file, feature, platform),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Extract timeout')), 3000))
                    ]);
                    if (example) {
                        results.push(example);
                    }
                }
                catch (error) {
                    console.log(`⚠️ 提取代码示例超时或失败: ${file.name}`);
                    continue;
                }
            }
        }
        catch (error) {
            console.log(`⚠️ 搜索路径失败: ${path}`, error instanceof Error ? error.message : String(error));
        }
        return results;
    }
    /**
     * 检查文件是否相关
     */
    isRelevantFile(filename, feature, language) {
        const featureLower = feature.toLowerCase();
        const filenameLower = filename.toLowerCase();
        // 检查文件扩展名
        const validExtensions = language === 'lua' ? ['.lua'] :
            language === 'cpp' ? ['.cpp', '.h', '.cc', '.cxx'] :
                ['.cpp', '.h', '.cc', '.cxx', '.lua'];
        const hasValidExtension = validExtensions.some(ext => filename.endsWith(ext));
        if (!hasValidExtension)
            return false;
        // 检查文件名是否包含特征词
        const featureKeywords = this.extractFeatureKeywords(feature);
        return featureKeywords.some(keyword => filenameLower.includes(keyword.toLowerCase()));
    }
    /**
     * 提取特征关键词
     */
    extractFeatureKeywords(feature) {
        const keywords = [feature];
        // 添加相关关键词
        const keywordMap = {
            'sprite': ['sprite', 'image', 'texture'],
            'animation': ['animation', 'animate', 'action'],
            'physics': ['physics', 'body', 'collision'],
            'audio': ['audio', 'sound', 'music'],
            'ui': ['ui', 'button', 'label', 'widget'],
            'scene': ['scene', 'layer', 'node'],
            'input': ['input', 'touch', 'keyboard', 'mouse'],
            'render': ['render', 'draw', 'opengl'],
            '3d': ['3d', 'mesh', 'camera', 'light'],
            'particle': ['particle', 'effect']
        };
        const featureLower = feature.toLowerCase();
        Object.entries(keywordMap).forEach(([key, values]) => {
            if (featureLower.includes(key) || values.some(v => featureLower.includes(v))) {
                keywords.push(...values);
            }
        });
        return [...new Set(keywords)];
    }
    /**
     * 提取代码示例
     */
    async extractCodeExample(file, feature, platform) {
        try {
            const response = await networkUtils.get(file.download_url, { timeout: 8000 });
            const content = response.data;
            // 检查内容是否包含相关特征
            if (!this.isContentRelevant(content, feature)) {
                return null;
            }
            // 提取相关代码段
            const codeSegments = this.extractRelevantCodeSegments(content, feature);
            if (codeSegments.length === 0) {
                return null;
            }
            const language = file.name.endsWith('.lua') ? 'lua' : 'cpp';
            return {
                title: this.generateExampleTitle(file.name, feature),
                language,
                code: codeSegments.join('\n\n// ...\n\n'),
                description: this.generateExampleDescription(file.name, feature, codeSegments),
                platform: this.detectPlatform(content, platform),
                version: 'latest',
                sourceUrl: file.html_url
            };
        }
        catch (error) {
            console.log(`⚠️ 提取代码示例失败: ${file.name}`);
            return null;
        }
    }
    /**
     * 检查内容是否相关
     */
    isContentRelevant(content, feature) {
        const contentLower = content.toLowerCase();
        const featureKeywords = this.extractFeatureKeywords(feature);
        return featureKeywords.some(keyword => contentLower.includes(keyword.toLowerCase()));
    }
    /**
     * 提取相关代码段
     */
    extractRelevantCodeSegments(content, feature) {
        const lines = content.split('\n');
        const segments = [];
        const featureKeywords = this.extractFeatureKeywords(feature);
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const lineLower = line.toLowerCase();
            // 检查是否包含特征关键词
            if (featureKeywords.some(keyword => lineLower.includes(keyword.toLowerCase()))) {
                // 提取上下文（前后10行）
                const start = Math.max(0, i - 10);
                const end = Math.min(lines.length, i + 11);
                const segment = lines.slice(start, end).join('\n');
                segments.push(segment);
                i = end; // 跳过已处理的行
            }
        }
        return segments.slice(0, 3); // 限制段数
    }
    /**
     * 生成示例标题
     */
    generateExampleTitle(filename, feature) {
        const baseName = filename.replace(/\.(cpp|h|lua)$/, '');
        return `${feature} 示例 - ${baseName}`;
    }
    /**
     * 生成示例描述
     */
    generateExampleDescription(filename, feature, segments) {
        return `从 ${filename} 中提取的 ${feature} 相关代码示例，包含 ${segments.length} 个代码段。`;
    }
    /**
     * 检测平台
     */
    detectPlatform(content, requestedPlatform) {
        if (requestedPlatform !== 'all')
            return requestedPlatform;
        const contentLower = content.toLowerCase();
        if (contentLower.includes('android') || contentLower.includes('jni'))
            return 'android';
        if (contentLower.includes('ios') || contentLower.includes('objective-c'))
            return 'ios';
        if (contentLower.includes('windows') || contentLower.includes('win32'))
            return 'windows';
        if (contentLower.includes('mac') || contentLower.includes('cocoa'))
            return 'mac';
        if (contentLower.includes('linux'))
            return 'linux';
        if (contentLower.includes('web') || contentLower.includes('emscripten'))
            return 'web';
        return 'cross-platform';
    }
    /**
     * 搜索测试代码
     */
    async searchTestCode(feature, platform, options) {
        // 实现测试代码搜索逻辑
        return this.searchExamplesInPath('tests', feature, platform, options);
    }
    /**
     * 搜索源码示例
     */
    async searchSourceCodeExamples(feature, platform, options) {
        // 实现源码示例搜索逻辑
        const results = [];
        const sourcePaths = ['core/2d', 'core/3d', 'core/ui', 'core/audio', 'core/physics'];
        for (const path of sourcePaths) {
            const examples = await this.searchExamplesInPath(path, feature, platform, options);
            results.push(...examples.slice(0, 2)); // 限制每个路径的结果数量
        }
        return results;
    }
    /**
     * 按质量排序示例
     */
    sortExamplesByQuality(examples, feature) {
        return examples.sort((a, b) => {
            // 按代码长度排序（更长的代码通常更完整）
            const lengthScore = b.code.length - a.code.length;
            // 按标题相关性排序
            const titleRelevanceA = a.title.toLowerCase().includes(feature.toLowerCase()) ? 100 : 0;
            const titleRelevanceB = b.title.toLowerCase().includes(feature.toLowerCase()) ? 100 : 0;
            return (titleRelevanceB - titleRelevanceA) || (lengthScore * 0.1);
        });
    }
}
// 导出默认代码示例服务实例
export const codeExampleService = new CodeExampleService();
//# sourceMappingURL=codeExampleService.js.map